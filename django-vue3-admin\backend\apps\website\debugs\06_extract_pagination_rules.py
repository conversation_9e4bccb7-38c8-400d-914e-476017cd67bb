"""
Content Section 翻页规则提取脚本
分析content_section页面的HTML内容，识别翻页模式并生成翻页规则
"""

from base import init_debug
init_debug()

from base_config import site, check_site_config
from apps.website.models import SiteMap
from apps.website.service.html_parser import HtmlParser
from apps.website.service.ai_service import ai_service
from apps.website.enums import WebsiteContentType
import json
import logging

logger = logging.getLogger(__name__)


def analyze_pagination_pattern(url: str, html_content: str) -> dict:
    """
    使用AI分析页面的翻页模式
    
    Args:
        url: 页面URL
        html_content: HTML内容
        
    Returns:
        dict: 翻页模式分析结果
    """
    try:
        # 调用AI服务分析翻页模式
        result = ai_service.analyze_pagination_pattern(url, html_content)
        
        if result and result.get('success'):
            # AI服务已经处理了JSON解析，直接返回结果
            return result
        else:
            error_msg = result.get('error', '未知错误') if result else 'AI服务无响应'
            return {
                'success': False,
                'error': f'AI分析失败: {error_msg}',
                'url': url
            }
            
    except Exception as e:
        logger.error(f"翻页模式分析异常: {e}")
        return {
            'success': False,
            'error': str(e),
            'url': url
        }


def extract_pagination_rules_from_content_sections(limit: int = 10):
    """
    从content_section页面中提取翻页规则
    
    Args:
        limit: 处理的最大页面数量
    """
    if not check_site_config():
        return
    
    if site is None:
        print("❌ 站点配置为空，脚本终止")
        return
    
    print(f"=== 开始分析站点 {site.name} 的Content Section翻页规则 ===")
    
    # 获取所有content_section类型的页面
    content_sections = SiteMap.objects.filter(
        site_id=site.id,
        content_type_ai=WebsiteContentType.CONTENT_SECTION.value
    ).exclude(
        html_content__isnull=True
    ).exclude(
        html_content=''
    )[:limit]
    
    total_count = content_sections.count()
    print(f"📊 发现 {total_count} 个Content Section页面（含HTML内容）")
    
    if total_count == 0:
        print("❌ 没有找到Content Section页面或页面缺少HTML内容")
        print("💡 建议先运行 03_update_content_type_by_ai.py 进行页面分类和HTML抓取")
        return
    
    html_parser = HtmlParser()
    results = []
    
    for i, sitemap in enumerate(content_sections, 1):
        print(f"\n🔍 [{i}/{total_count}] 分析页面: {sitemap.url}")
        print(f"   标题: {sitemap.title or '无标题'}")
        
        try:
            # 通过SiteMap统一获取HTML内容
            print("   📥 获取HTML内容...")
            try:
                html_content = sitemap.get_html_content()
                if html_content:
                    print(f"   ✅ HTML内容获取成功，长度: {len(html_content)} 字符")
                else:
                    print("   ❌ HTML内容为空，跳过")
                    continue
            except Exception as e:
                print(f"   ❌ HTML获取失败: {e}")
                continue
            
            # AI分析翻页模式
            print("   🤖 AI分析翻页模式...")
            analysis_result = analyze_pagination_pattern(sitemap.url, html_content)
            
            if analysis_result.get('success'):
                pagination_info = analysis_result.get('pagination_info', {})
                has_pagination = pagination_info.get('has_pagination', False)
                
                if has_pagination:
                    print(f"   ✅ 发现翻页: {pagination_info.get('pagination_type', '未知类型')}")
                    print(f"      下一页选择器: {pagination_info.get('next_page_selector', '无')}")
                    print(f"      URL模式: {pagination_info.get('url_pattern', '无')}")
                    
                    # 保存翻页信息到结果
                    results.append({
                        'url': sitemap.url,
                        'title': sitemap.title,
                        'pagination_info': pagination_info,
                        'status': 'has_pagination'
                    })
                else:
                    print("   ❌ 未发现翻页模式")
                    results.append({
                        'url': sitemap.url,
                        'title': sitemap.title,
                        'pagination_info': pagination_info,
                        'status': 'no_pagination'
                    })
            else:
                error_message = analysis_result.get('error', '未知错误')
                print(f"   ❌ 分析失败: {error_message}")
                results.append({
                    'url': sitemap.url,
                    'title': sitemap.title,
                    'error': error_message,
                    'status': 'error'
                })
                
        except Exception as e:
            print(f"   ❌ 处理异常: {e}")
            results.append({
                'url': sitemap.url,
                'title': sitemap.title,
                'error': str(e),
                'status': 'exception'
            })
    
    # 输出结果汇总
    print(f"\n📊 翻页规则提取结果汇总:")
    
    has_pagination_count = len([r for r in results if r.get('status') == 'has_pagination'])
    no_pagination_count = len([r for r in results if r.get('status') == 'no_pagination'])
    error_count = len([r for r in results if r.get('status') in ['error', 'exception']])
    
    print(f"   总数: {total_count}")
    print(f"   有翻页: {has_pagination_count}")
    print(f"   无翻页: {no_pagination_count}")
    print(f"   出错: {error_count}")
    
    # 显示有翻页的页面详情
    if has_pagination_count > 0:
        print(f"\n📄 发现翻页的页面:")
        for result in results:
            if result.get('status') == 'has_pagination':
                pagination_info = result.get('pagination_info', {})
                print(f"   • {result['title']}")
                print(f"     URL: {result['url']}")
                print(f"     类型: {pagination_info.get('pagination_type', '未知')}")
                print(f"     下一页: {pagination_info.get('next_page_selector', '无')}")
                print(f"     URL模式: {pagination_info.get('url_pattern', '无')}")
                print()
    
    return results


def main():
    """主函数"""
    print("=== Content Section 翻页规则提取工具 ===")
    
    # 检查站点配置
    if not check_site_config():
        return
    
    if site is not None:
        print(f"默认站点: {site.name}")
        print(f"站点域名: {site.domain}")
    else:
        print("❌ 站点配置为空")
        return
    print()
    
    # 执行翻页规则提取
    results = extract_pagination_rules_from_content_sections(limit=10)
    
    print(f"\n🎉 翻页规则提取完成！")


if __name__ == "__main__":
    main() 