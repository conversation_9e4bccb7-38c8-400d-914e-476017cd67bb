"""
URL分析服务V2
专注于URL提取、分类和模式分析
"""

import logging
from typing import Dict, List, Set, Tuple, Optional
from urllib.parse import urlparse, urljoin
from collections import defaultdict, Counter

from .html_parser import HtmlParser

logger = logging.getLogger(__name__)


class URLClassifier:
    """URL分类器"""
    
    def __init__(self, base_domain: str):
        """
        初始化分类器
        
        Args:
            base_domain: 基础域名（如：wulanchabu.gov.cn）
        """
        self.base_domain = base_domain.lower()
        
        # 资源文件扩展名
        self.resource_extensions = {
            # 图片
            'image': {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.svg', '.ico'},
            # 脚本和样式
            'script': {'.js', '.css'},
            # 文档
            'document': {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx', '.txt'},
            # 压缩文件
            'archive': {'.zip', '.rar', '.7z', '.tar', '.gz'},
            # 视频音频
            'media': {'.mp4', '.avi', '.mov', '.wmv', '.mp3', '.wav', '.ogg'},
            # 其他
            'other': {'.xml', '.json', '.rss', '.atom'}
        }
        
        # 所有资源扩展名的集合
        self.all_resource_extensions = set()
        for extensions in self.resource_extensions.values():
            self.all_resource_extensions.update(extensions)
    
    def classify_url(self, url_info: Dict[str, str]) -> Dict[str, str]:
        """
        分类单个URL
        
        Args:
            url_info: URL信息字典，包含url、title、type字段
            
        Returns:
            分类结果字典
        """
        try:
            url = url_info.get('url', '')
            title = url_info.get('title', '')
            url_type = url_info.get('type', 'link')
            
            parsed = urlparse(url.lower())
            domain = parsed.netloc
            path = parsed.path.lower()
            
            # 检查是否为资源文件
            resource_type = self._get_resource_type(path)
            if resource_type:
                return {
                    'type': 'resource',
                    'subtype': resource_type,
                    'domain': domain,
                    'url': url,
                    'title': title,
                    'url_type': url_type
                }
            
            # 分类域名类型
            if not domain:
                # 相对URL
                return {
                    'type': 'current_domain',
                    'subtype': 'relative',
                    'domain': self.base_domain,
                    'url': url,
                    'title': title,
                    'url_type': url_type
                }
            elif domain == self.base_domain or domain == f'www.{self.base_domain}':
                # 当前域名
                return {
                    'type': 'current_domain',
                    'subtype': 'absolute',
                    'domain': domain,
                    'url': url,
                    'title': title,
                    'url_type': url_type
                }
            elif domain.endswith(f'.{self.base_domain}'):
                # 下级域名
                return {
                    'type': 'subdomain',
                    'subtype': 'subdomain',
                    'domain': domain,
                    'url': url,
                    'title': title,
                    'url_type': url_type
                }
            else:
                # 外部域名
                return {
                    'type': 'external',
                    'subtype': 'external',
                    'domain': domain,
                    'url': url,
                    'title': title,
                    'url_type': url_type
                }
                
        except Exception as e:
            logger.error(f"URL分类失败 {url_info}: {e}")
            return {
                'type': 'unknown',
                'subtype': 'error',
                'domain': '',
                'url': url_info.get('url', ''),
                'title': url_info.get('title', ''),
                'url_type': url_info.get('type', 'link')
            }
    
    def _get_resource_type(self, path: str) -> str:
        """
        获取资源类型
        
        Args:
            path: URL路径
            
        Returns:
            资源类型，如果不是资源则返回空字符串
        """
        if not path:
            return ''
        
        # 获取文件扩展名
        if '.' in path:
            extension = '.' + path.split('.')[-1].lower()
            
            # 检查是否为资源文件
            for resource_type, extensions in self.resource_extensions.items():
                if extension in extensions:
                    return resource_type
        
        return ''
    
    def classify_urls_batch(self, url_infos: List[Dict[str, str]]) -> Dict[str, List[Dict[str, str]]]:
        """
        批量分类URL
        
        Args:
            url_infos: URL信息列表
            
        Returns:
            分类结果字典
        """
        results = {
            'current_domain': [],
            'subdomain': [],
            'external': [],
            'resource': [],
            'unknown': []
        }
        
        for url_info in url_infos:
            classification = self.classify_url(url_info)
            url_type = classification['type']
            results[url_type].append(classification)
        
        return results


class URLExtractor:
    """URL提取器"""
    
    def __init__(self, html_parser: HtmlParser):
        """
        初始化提取器
        
        Args:
            html_parser: HTML解析器实例
        """
        self.parser = html_parser
    
    def extract_all_urls(self, html_content: str, base_url: str) -> List[Dict[str, str]]:
        """
        提取页面中所有类型的URL（包含标题信息）
        
        Args:
            html_content: HTML内容
            base_url: 基础URL
            
        Returns:
            URL信息列表，格式: [{"url": "链接地址", "title": "标题", "type": "link|resource"}]
        """
        if not html_content:
            return []
        
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            url_infos = []
            seen_urls = set()
            
            # 提取链接URL（带标题）
            for tag in soup.find_all('a', href=True):
                href = tag.get('href', '').strip()
                if href and not href.startswith('#') and not href.startswith('javascript:') and not href.startswith('mailto:'):
                    absolute_url = urljoin(base_url, href)
                    if absolute_url not in seen_urls:
                        seen_urls.add(absolute_url)
                        
                        # 获取链接标题
                        title = tag.get_text(strip=True) or tag.get('title', '').strip()
                        
                        url_infos.append({
                            'url': absolute_url,
                            'title': title,
                            'type': 'link'
                        })
            
            # 提取图片URL
            for tag in soup.find_all('img', src=True):
                src = tag.get('src', '').strip()
                if src:
                    absolute_url = urljoin(base_url, src)
                    if absolute_url not in seen_urls:
                        seen_urls.add(absolute_url)
                        
                        # 图片的alt属性作为标题
                        title = tag.get('alt', '').strip()
                        
                        url_infos.append({
                            'url': absolute_url,
                            'title': title,
                            'type': 'resource'
                        })
            
            # 提取CSS文件URL
            for tag in soup.find_all('link', href=True):
                href = tag.get('href', '').strip()
                if href:
                    absolute_url = urljoin(base_url, href)
                    if absolute_url not in seen_urls:
                        seen_urls.add(absolute_url)
                        
                        url_infos.append({
                            'url': absolute_url,
                            'title': '',  # CSS文件不需要标题
                            'type': 'resource'
                        })
            
            # 提取JS文件URL
            for tag in soup.find_all('script', src=True):
                src = tag.get('src', '').strip()
                if src:
                    absolute_url = urljoin(base_url, src)
                    if absolute_url not in seen_urls:
                        seen_urls.add(absolute_url)
                        
                        url_infos.append({
                            'url': absolute_url,
                            'title': '',  # JS文件不需要标题
                            'type': 'resource'
                        })
            
            # 提取其他资源URL（iframe、embed、object等）
            for tag_name in ['iframe', 'embed', 'object', 'source', 'track']:
                for tag in soup.find_all(tag_name):
                    for attr in ['src', 'data', 'href']:
                        if tag.has_attr(attr):
                            url = tag.get(attr, '').strip()
                            if url and not url.startswith('#'):
                                absolute_url = urljoin(base_url, url)
                                if absolute_url not in seen_urls:
                                    seen_urls.add(absolute_url)
                                    
                                    # iframe可能有标题
                                    title = ''
                                    if tag_name == 'iframe':
                                        title = tag.get('title', '').strip()
                                    
                                    url_infos.append({
                                        'url': absolute_url,
                                        'title': title,
                                        'type': 'resource'
                                    })
            
            logger.info(f"从 {base_url} 提取到 {len(url_infos)} 个URL")
            return url_infos
            
        except Exception as e:
            logger.error(f"提取URL失败: {e}")
            return []


class URLAnalysisServiceV2:
    """URL分析服务V2"""
    
    def __init__(self, timeout: int = 30):
        """
        初始化服务
        
        Args:
            timeout: HTTP请求超时时间
        """
        self.html_parser = HtmlParser(timeout=timeout)
        self.timeout = timeout
    
    def extract_and_classify_urls_from_html(self, html_content: str, base_url: str, base_domain: str) -> Dict[str, any]:
        """
        从HTML内容中提取并分类URL（推荐使用的方法）
        
        Args:
            html_content: 已获取的HTML内容
            base_url: 页面基础URL
            base_domain: 基础域名
            
        Returns:
            分析结果字典
        """
        return self.analyze_page_urls(html_content, base_url, base_domain)
    
    def extract_and_classify_urls(self, url: str, base_domain: str) -> Dict[str, any]:
        """
        提取并分类URL（废弃方法，建议使用extract_and_classify_urls_from_html）
        
        Args:
            url: 要分析的页面URL
            base_domain: 基础域名
            
        Returns:
            分析结果字典
        """
        import warnings
        warnings.warn(
            "extract_and_classify_urls方法已废弃，建议使用extract_and_classify_urls_from_html方法结合SiteMap.get_html_content()来获取HTML内容",
            DeprecationWarning,
            stacklevel=2
        )
        
        try:
            logger.warning(f"使用了废弃的extract_and_classify_urls方法分析URL: {url}")
            
            # 抓取页面内容
            html_content = self.html_parser.fetch_page_content(url)
            if not html_content:
                return {
                    'success': False,
                    'error': '页面抓取失败',
                    'url': url
                }
            
            # 调用新的方法进行分析
            return self.extract_and_classify_urls_from_html(html_content, url, base_domain)
            
        except Exception as e:
            logger.error(f"URL分析失败 {url}: {e}")
            return {
                'success': False,
                'error': str(e),
                'url': url
            }
    
    def analyze_page_urls(self, html_content: str, base_url: str, base_domain: str) -> Dict[str, any]:
        """
        分析页面URL（直接使用HTML内容）
        
        Args:
            html_content: HTML内容
            base_url: 基础URL
            base_domain: 基础域名
            
        Returns:
            分析结果字典
        """
        try:
            logger.info(f"开始分析页面URL: {base_url}")
            
            # 提取页面信息
            page_info = self.html_parser.extract_page_info(html_content)
            
            # 初始化URL提取器和分类器
            url_extractor = URLExtractor(self.html_parser)
            url_classifier = URLClassifier(base_domain)
            
            # 提取所有URL
            all_url_infos = url_extractor.extract_all_urls(html_content, base_url)
            
            # 分类URL
            classifications = url_classifier.classify_urls_batch(all_url_infos)
            
            # 生成统计信息
            stats = self._generate_statistics(classifications)
            
            # 分析URL模式
            url_patterns = self._analyze_url_patterns(classifications.get('current_domain', []))
            
            return {
                'success': True,
                'url': base_url,
                'page_info': page_info,
                'classifications': classifications,
                'statistics': stats,
                'url_patterns': url_patterns,
                'html_size': len(html_content)
            }
            
        except Exception as e:
            logger.error(f"页面URL分析失败 {base_url}: {e}")
            return {
                'success': False,
                'error': str(e),
                'url': base_url
            }
    
    def _generate_statistics(self, classifications: Dict[str, List[Dict[str, str]]]) -> Dict[str, any]:
        """
        生成统计信息
        
        Args:
            classifications: 分类结果
            
        Returns:
            统计信息字典
        """
        stats = {
            'total_urls': 0,
            'by_type': {},
            'by_domain': defaultdict(int),
            'by_resource_type': defaultdict(int),
            'top_domains': [],
            'top_resource_types': []
        }
        
        # 统计总数和各类型数量
        for url_type, urls in classifications.items():
            count = len(urls)
            stats['total_urls'] += count
            stats['by_type'][url_type] = count
            
            # 统计域名
            for url_info in urls:
                domain = url_info.get('domain', '')
                if domain:
                    stats['by_domain'][domain] += 1
                
                # 统计资源类型
                if url_type == 'resource':
                    subtype = url_info.get('subtype', '')
                    if subtype:
                        stats['by_resource_type'][subtype] += 1
        
        # 获取Top域名
        stats['top_domains'] = sorted(
            stats['by_domain'].items(),
            key=lambda x: x[1],
            reverse=True
        )[:10]
        
        # 获取Top资源类型
        stats['top_resource_types'] = sorted(
            stats['by_resource_type'].items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        return stats
    
    def _analyze_url_patterns(self, current_domain_urls: List[Dict[str, str]]) -> List[Dict[str, any]]:
        """
        分析URL模式
        
        Args:
            current_domain_urls: 当前域名的URL列表
            
        Returns:
            URL模式列表
        """
        patterns = []
        
        if not current_domain_urls:
            return patterns
        
        # 按路径分组
        path_groups = defaultdict(list)
        for url_info in current_domain_urls:
            url = url_info.get('url', '')
            try:
                parsed = urlparse(url)
                path = parsed.path
                path_groups[path].append(url_info)
            except Exception:
                continue
        
        # 分析常见模式
        for path, urls in path_groups.items():
            if len(urls) >= 1:  # 至少有1个URL
                patterns.append({
                    'pattern': path,
                    'content_type': 'unknown',  # 默认为unknown
                    'link_count': len(urls),
                    'sample_urls': [url_info.get('url') for url_info in urls[:3]],
                    'sample_titles': [url_info.get('title', '') for url_info in urls[:3]]
                })
        
        # 按链接数量排序
        patterns.sort(key=lambda x: x['link_count'], reverse=True)
        
        return patterns
    
    def get_subdomain_urls(self, classifications: Dict[str, List[Dict[str, str]]]) -> List[Dict[str, str]]:
        """
        获取子域名URL列表
        
        Args:
            classifications: 分类结果
            
        Returns:
            子域名URL列表
        """
        return classifications.get('subdomain', [])
    
    def get_current_domain_urls(self, classifications: Dict[str, List[Dict[str, str]]]) -> List[Dict[str, str]]:
        """
        获取当前域名URL列表
        
        Args:
            classifications: 分类结果
            
        Returns:
            当前域名URL列表
        """
        return classifications.get('current_domain', [])
    
    def generate_markdown_table(self, classifications: Dict[str, List[Dict[str, str]]], base_domain: str) -> str:
        """
        生成当前域名URL的Markdown表格
        
        Args:
            classifications: 分类结果
            base_domain: 基础域名
            
        Returns:
            Markdown格式的表格字符串
        """
        current_domain_urls = classifications.get('current_domain', [])
        
        if not current_domain_urls:
            return "# 当前域名URL列表\n\n未找到当前域名的URL。\n"
        
        markdown_lines = [
            f"# {base_domain} 域名URL列表",
            "",
            f"总计：{len(current_domain_urls)} 个链接",
            "",
            "| 链接地址 | 标题 |",
            "|----------|------|"
        ]
        
        for url_info in current_domain_urls:
            url = url_info.get('url', '')
            title = url_info.get('title', '').replace('|', '\\|')  # 转义管道符
            if not title:
                title = "无标题"
            markdown_lines.append(f"| {url} | {title} |")
        
        return '\n'.join(markdown_lines) 