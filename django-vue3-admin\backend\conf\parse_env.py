
import os

default_env = 'dev'
def parse_env():
    # 判断是否在单元测试环境中
    if os.getenv('PYTEST_VERSION') is not None:
        print("检测到单元测试环境，强制设置为 dev 环境")
        return 'dev'
    ENV_NAME = os.getenv('DJANGO_ENV')  # 通过环境变量获取环境标识
    if ENV_NAME is None:
        print(f"DJANGO_ENV 环境变量未设置，使用默认环境: {default_env}")
        ENV_NAME = default_env
    else:
        print(f"DJANGO_ENV 环境变量为: {ENV_NAME}")
    return ENV_NAME
