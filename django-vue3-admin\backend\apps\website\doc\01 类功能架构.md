
# Website通用爬虫系统 - 类功能架构

## 核心架构概览

### 数据模型层 (model/)
- **site.py**: 站点基础信息模型，支持父子站点关系
- **sitemap.py**: 站点地图模型，**核心数据访问层**，统一HTML获取接口
- **column.py**: 栏目页管理模型，包含分页抓取状态
- **column_rule.py**: 栏目规则配置模型，包含内容列表提取和分页规则
- **content.py**: 最终内容页数据存储模型
- **content_extraction_rule.py**: 内容页提取规则配置模型
- **html.py**: 原始HTML缓存模型，支持压缩存储
- **dify_call_log.py**: AI服务调用日志模型

### 核心服务层 (service/)

#### 🔧 集成服务 (核心编排)
- **integrated_crawler_service.py**: **核心集成服务**，整合4步爬虫流程
- **site_service.py**: 站点管理服务
- **site_map_service.py**: 站点地图服务，**统一数据访问入口**

#### 🔍 URL分析服务
- **url_analysis_service_v2.py**: **URL分析核心**，支持统一HTML获取机制
- **content_type_matcher.py**: 内容类型匹配器，基于正则规则分类URL

#### 🤖 AI集成服务
- **ai_service.py**: AI服务统一门面，整合Dify和Coze
- **ai_services/dify_service.py**: Dify平台集成服务
- **ai_services/coze_service.py**: 扣子平台集成服务
- **ai_config_generator.py**: AI驱动的配置生成器

#### 📄 HTML处理服务
- **html_service.py**: **HTML缓存管理服务**，统一HTML获取和缓存
- **html_parser.py**: HTML解析器，支持Playwright和requests
- **html_snippet_extractor.py**: HTML片段提取器

#### 🗂️ 栏目处理服务
- **column_service.py**: 栏目内容抓取服务，负责分页抓取和内容发现
- **column_rule_manager.py**: 栏目规则管理器，处理规则生成、测试、关联
- **column_browser_pagination_handler.py**: **浏览器分页处理器**，Playwright翻页
- **column_page_extractor.py**: 栏目页面内容提取器

#### 📰 内容处理服务
- **content_service.py**: 内容提取服务，提取标题、正文、时间等信息
- **content_extractor.py**: 内容提取器
- **content_extraction_rule_generator.py**: 内容提取规则生成器
- **content_extraction_rule_validator.py**: 内容提取规则验证器

#### 🔄 分页处理器 (pagination_handlers/)
- **base_handler.py**: 分页处理器基类
- **browser_automation_handler.py**: 浏览器自动化分页处理
- **unified_browser_handler.py**: **统一浏览器分页处理** (推荐方案)
- **direct_http_handler.py**: 直接HTTP分页处理
- **handler_factory.py**: 分页处理器工厂

#### 🔍 验证服务 (validation/)
- **content_extraction_validator.py**: 内容提取规则验证服务
- **content_extraction_service.py**: 内容提取验证服务

#### 🛠️ 工具服务
- **concurrent_utils.py**: 并发处理工具
- **crawl/page_analyzer_crawler_adapter.py**: 页面分析适配器

### 调试脚本层 (debugs/)

#### 🚀 集成流程脚本 (00_系列)
- **00_integrated_crawler_*.py**: 各站点完整集成流程脚本
- **integrated_crawler_runner.py**: **集成脚本运行器基础类**

#### 📋 分步调试脚本
- **01_create_site.py**: 站点创建和深度初始化
- **02_1_analysis_site_rules.py**: URL分析报告生成
- **02_2_applay_rules_to_sitemap.py**: 规则应用到站点地图
- **03_update_content_type_by_ai.py**: AI内容分类 (单线程)
- **03_update_content_type_by_ai_concurrent.py**: AI内容分类 (并发)

#### 🔧 功能测试脚本 (06_系列)
- **06_extract_pagination_rules.py**: 分页规则提取
- **06_generate_ai_config_*.py**: AI配置生成
- **06_2_test_content_extraction_*.py**: 内容提取测试
- **06_4_test_crawl_column_*.py**: 栏目抓取测试

#### 📊 内容抓取脚本 (07_、08_系列)
- **07_first_crawl_content_sections_*.py**: 首次内容区域抓取
- **08_update_content_sections_*.py**: 增量内容更新

#### 🛠️ 工具脚本
- **10_debug_sitemap_crawl_task.py**: SiteMap批量HTML缓存创建
- **content_section_crawler_runner.py**: 内容区域爬虫运行器
- **base.py**: 调试脚本初始化基础类
- **clear_migration_and_fake_migrat.py**: 数据库迁移清理工具

### 配置和数据 

#### 📁 配置文件 (configs/)
- **pagination_rules/**: 分页规则配置
  - **government_sites.json**: 政府网站分页规则
  - **unified_browser_config.json**: 统一浏览器配置

#### 🔤 枚举定义 (enums.py)
- **CrawlScope**: 抓取范围 (当前域名、发现子域名、站群全部、已知站点)
- **WebsiteContentType**: 内容类型 (首页、栏目页、内容页、服务页等)
- **PaginationType**: 分页处理方式 (浏览器自动化、直接HTTP、统一浏览器处理、无分页)
- **CrawlMode**: 抓取模式 (深度抓取、增量抓取)

#### 📦 数据传输对象 (beans.py)
- **PaginationConfigBean**: 分页配置Bean
- **ContentExtractionBean**: 内容提取配置Bean
- **ContentBean**: 内容Bean

#### 🧪 测试数据 (tests/)
- **data/**: 各站点测试HTML数据 (immu、jhsjk、wulanchabu、xlgl)
- **units/**: 单元测试
- **integrations/**: 集成测试
- **test_output/**: 测试输出结果

### 文档系统 (doc/)

#### 📚 架构文档
- **00 核心信息.md**: 系统核心信息总览
- **01 类功能架构.md**: 本文档，完整架构说明
- **02 Todo.md**: 待办事项追踪
- **03 调整记录.md**: 系统调整历史记录

#### 🤖 AI提示语模板
- **提示语-分析Column翻页模式.md**: 栏目翻页模式分析
- **提示语-分析Content内容提取规则.md**: 内容提取规则分析
- **提示语-根据HTML内容分析URL是否为Column.md**: 栏目URL识别
- **提示语-根据首页Url分析Content Url正则规则.md**: 内容URL规则生成

#### 📝 设计文档
- **站群子站点功能说明.md**: 站群架构设计
- **站群抓取设计.md**: 站群抓取策略
- **系统重构分析报告.md**: 重构分析和建议

## 🔑 关键设计原则

### 统一HTML获取机制 ✅
- **所有HTML获取都通过SiteMap.get_html_content()进行**
- 充分利用HTML缓存机制，避免重复抓取
- HtmlService提供统一的HTML缓存管理

### AI驱动的智能适配 🤖
- 自动识别网站特征，生成专用抓取规则
- 支持Dify和Coze多平台AI服务
- 智能内容类型分类和栏目发现

### 通用化设计 🌐
- **理论上支持任意网站结构**
- 统一浏览器处理方案，避免复杂技术分析
- 模块化架构，易于维护和扩展

### 数据一致性保障 🔒
- 首页URL拥有对应的SiteMap记录
- 统一的数据访问层设计
- 完善的缓存和状态管理机制