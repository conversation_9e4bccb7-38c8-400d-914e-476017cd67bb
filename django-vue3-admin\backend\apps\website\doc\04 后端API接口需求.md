# Website爬虫系统 - 后端API接口需求

## 背景
用户需要为Website爬虫系统开发对应的后端界面，需要设计API接口来驱动整个业务流程。

## 核心需求

### 业务驱动需求
- 需要能够驱动整个爬虫业务流程的关键接口
- 不需要简单的增删改查接口
- 接口要贴合4步爬虫流程的业务逻辑
- 重点关注核心业务价值，而非通用功能


### 技术背景
- 基于Django REST Framework
- 支持长时间运行的爬虫任务
- 集成AI服务（Dify、Coze）
- 使用Playwright进行浏览器自动化
- 统一的HTML获取和缓存机制

## 具体接口需求点

### 1. 站点初始化接口 (step01_create_site)
**功能**: 输入URL，创建网站并进行深度初始化  
**参数**: `{name, domain, start_url, description}`  
**处理流程**:
- 创建Site记录
- 为首页创建SiteMap记录  
- 通过统一HTML获取机制抓取首页
- AI分析首页链接，分类当前域名和子域名URL
- 批量创建SiteMap记录
- 发现并创建子站点
- 返回初始化统计信息

### 2. 规则生成与应用接口 (step02_2_apply_rules_to_sitemap)  
**功能**: AI生成内容URL正则规则并应用分类  
**参数**: `{site_id}`  
**处理流程**:
- 检查站点是否有URL匹配规则
- 如无规则则调用AI自动生成（最多重试3次）
- 应用规则对所有SiteMap进行正则匹配
- 确定内容类型（content/column/service/unknown等）
- 验证规则有效性（避免所有URL都分类为unknown）
- 返回分类统计结果

### 3. AI内容类型判断接口 (step03_update_content_type_by_ai)
**功能**: 对非内容URL进行AI智能分类  
**参数**: `{site_id, limit=50}`  
**处理流程**:
- 筛选规则无法识别且AI未判定的SiteMap记录
- 批量调用AI服务进行内容类型判断
- 支持中断处理（Ctrl+C优雅停止）
- 更新SiteMap的content_type_ai字段
- 发现新链接时创建新的SiteMap记录
- 返回处理进度和结果统计

### 4. 完整流程编排接口 (run_full_pipeline)
**功能**: 一键执行完整的网站分析流程  
**参数**: `{site_config, ai_limit=50}`  
**处理流程**:
- 依次执行步骤1、2、3
- 每步失败则终止后续步骤
- 返回各步骤执行结果和整体成功状态
- 适合新网站的完整初始化

### 5. 站点状态查询接口
**功能**: 获取站点分析进度和统计信息  
**参数**: `{site_id}`  
**返回信息**:
- SiteMap总数量和各类型分布统计
- AI分析进度（已处理/待处理数量）
- 规则应用状态和匹配效果
- 最近更新时间和错误日志

### 6. 批量任务监控接口
**功能**: 实时监控长时间运行任务状态  
**支持功能**:
- WebSocket实时推送任务进度
- 任务暂停/恢复/取消控制
- 错误日志实时查看
- 多任务并发状态管理

### 7. AI服务配置接口
**功能**: 管理AI服务调用配置  
**支持功能**:
- Dify/Coze服务切换和配置
- API密钥管理和验证
- 调用频率限制设置
- 服务可用性检测

### 8. 站点规则管理接口
**功能**: 手动管理URL匹配规则  
**支持功能**:
- 查看当前规则和匹配效果
- 手动添加/修改/删除规则
- 规则测试和验证
- 规则优先级调整 

## 约束条件
- 不开发简单的CRUD接口
- 重点关注业务流程驱动接口
- 接口要体现系统的AI驱动和自动化特性
- 需要考虑前端交互的便利性
