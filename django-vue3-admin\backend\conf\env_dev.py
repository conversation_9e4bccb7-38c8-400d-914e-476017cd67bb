import os
# 设置生产环境DEBUG为False
DEBUG = True
DATABASE_NAME = 'hyqm2_test' # mysql 时使用
# 数据库地址 改为自己数据库地址
DATABASE_HOST = 'mysql-test'
# # 数据库端口
DATABASE_PORT = 3306
# # 数据库用户名
DATABASE_USER = "root"
# # 数据库密码
DATABASE_PASSWORD = 'mysql_faQhf7'

# ================================================= #
# *************** 汇远轻媒数据库 配置  *************** #
# ================================================= #
HYQM_DATABASE_NAME = os.getenv('MYSQL_DB_HYQM', 'ruoyi-vue-pro-hyqm')
HYQM_DATABASE_USER = os.getenv('MYSQL_USER', 'root')
HYQM_DATABASE_PASSWORD = os.getenv('MYSQL_PASSWORD', 'UbMgd7LrNC')
HYQM_DATABASE_HOST = os.getenv('MYSQL_HOST', 'mysql-test')
HYQM_DATABASE_PORT = os.getenv('MYSQL_PORT', '3306')


REDIS_DB = 1
CELERY_BROKER_DB = 4
REDIS_PASSWORD = ''
REDIS_HOST = 'redis-test'
REDIS_PORT = 6379
REDIS_URL = f'redis://{REDIS_HOST}:{REDIS_PORT}/{REDIS_DB}'
S3_DEFAULT_BUCKET = 'hyqm2-test-**********'  # 默认存储桶

# 爱校对跳转链接 RPC 队列配置
AJD_JUMP_URL_QUEUE = 'rpc-queue-ajd-jump-url'
AJD_JUMP_URL_EXCHANGE = 'rpc-exchange-ajd-jump-url'
AJD_JUMP_URL_ROUTING_KEY = 'rpc-routing-ajd-jump-url'
AJD_FRONTEND_HOST= 'http://localhost:8000'

# 开启SQL日志
SQL_DEBUG = False

# ================================================= #
# ************** 测试服独立 Celery 配置  ************* #
# ================================================= #
# 覆盖基础配置，使用独立的Redis数据库来分离Celery队列
CELERY_BROKER_DB = 4  # 使用DB 4，与正式服的DB 3分离
CELERY_RESULT_DB = 2  # 使用DB 2，与正式服的DB 0分离

# 重新构建Celery配置URLs
CELERY_BROKER_URL = f'redis://{REDIS_HOST}:{REDIS_PORT}/{CELERY_BROKER_DB}'
CELERY_RESULT_BACKEND = f'redis://{REDIS_HOST}:{REDIS_PORT}/{CELERY_RESULT_DB}'

# ================================================= #
# ************** 测试服 RabbitMQ 队列隔离  *********** #
# ================================================= #
# 环境标识，用于队列名称后缀
ENVIRONMENT_SUFFIX = 'dev'
RPC_SUFFIX = 'dev'  # 保持与base配置一致的变量名

# 重新定义测试服专用的RabbitMQ队列和交换机名称
HYQM2_EXCHANGE_DEFAULT = f'hyqm2-exchange-default-{ENVIRONMENT_SUFFIX}'

# 重新定义HYQM2相关RPC队列名称，添加开发环境后缀
HYQM2_QUEUE_RPC_WINDOWS_CALL = f'hyqm2-queue-rpc-windows-call-{ENVIRONMENT_SUFFIX}'
HYQM2_QUEUE_RPC_CRAWL_WECHAT_ARTICLE_CONTENT = f'hyqm2-queue-rpc-crawl-wechat-official-account-article-content-{ENVIRONMENT_SUFFIX}'
HYQM2_QUEUE_RPC_JIAODUI = f'hyqm2-queue-rpc-jiaodui-{ENVIRONMENT_SUFFIX}'

