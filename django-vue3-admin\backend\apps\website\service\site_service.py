# -*- coding: utf-8 -*-
"""
站点服务
专门负责站点查找和管理
"""

import logging
from typing import Optional, List, Dict, Any
from urllib.parse import urlparse
from django.db.models import QuerySet
from django.utils import timezone

from apps.website.models import Site

logger = logging.getLogger(__name__)


class SiteService:
    """站点服务 - 专门负责站点查找和管理"""
    
    def __init__(self):
        """初始化服务"""
        # 延迟导入避免循环依赖
        from .site_map_service import SiteMapService
        from .url_analysis_service_v2 import URLAnalysisServiceV2
        
        self.site_map_service = SiteMapService()
        self.url_analysis_service = URLAnalysisServiceV2()
    
    def find_site_by_domain_pattern(self, domain_pattern: str) -> Optional[Site]:
        """
        根据域名模式查找站点
        
        Args:
            domain_pattern: 域名模式，支持部分匹配
            
        Returns:
            Site: 找到的站点对象，未找到返回None
        """
        try:
            # 查找包含指定域名模式的站点
            sites = Site.objects.filter(domain__icontains=domain_pattern)
            
            if sites.exists():
                logger.info(f"找到 {sites.count()} 个匹配域名模式 '{domain_pattern}' 的站点")
                # 返回第一个站点
                return sites.first()
            else:
                logger.warning(f"未找到匹配域名模式 '{domain_pattern}' 的站点")
                return None
                
        except Exception as e:
            logger.error(f"查找站点失败: {e}")
            return None
    
    def get_site_with_fallback(self, domain_pattern: str, site_description: str = "站点") -> Site:
        """
        获取站点并提供错误回退
        
        Args:
            domain_pattern: 域名模式
            site_description: 站点描述（用于错误信息）
            
        Returns:
            Site: 站点对象
            
        Raises:
            Exception: 未找到站点时抛出异常
        """
        site = self.find_site_by_domain_pattern(domain_pattern)
        
        if not site:
            print(f"   未找到{site_description}相关的Site记录，尝试查找所有站点...")
            available_sites = self.list_available_sites(limit=5)
            for site_info in available_sites:
                print(f"   可用站点: {site_info['name']} ({site_info['domain']})")
            raise Exception(f"未找到{site_description}相关的Site记录")
        
        print(f"   找到 {site_description} 站点: {site.name} ({site.domain})")
        return site
    
    def list_available_sites(self, limit: Optional[int] = None) -> List[dict]:
        """
        列出可用站点
        
        Args:
            limit: 限制返回数量，None表示不限制
            
        Returns:
            List[dict]: 站点信息列表
        """
        try:
            sites_query = Site.objects.all().order_by('name')
            
            if limit:
                sites_query = sites_query[:limit]
            
            sites_list = []
            for site in sites_query:
                sites_list.append({
                    'id': site.id,
                    'name': site.name,
                    'domain': site.domain
                })
            
            return sites_list
            
        except Exception as e:
            logger.error(f"列出可用站点失败: {e}")
            return []
    
    def get_site_by_id(self, site_id: int) -> Optional[Site]:
        """
        根据ID获取站点
        
        Args:
            site_id: 站点ID
            
        Returns:
            Site: 站点对象，未找到返回None
        """
        try:
            return Site.objects.get(id=site_id)
        except Site.DoesNotExist:
            logger.warning(f"未找到ID为 {site_id} 的站点")
            return None
        except Exception as e:
            logger.error(f"获取站点失败: {e}")
            return None
    
    def get_sites_by_main_domain(self, main_domain: str) -> QuerySet[Site]:
        """
        根据主域名获取站群中的所有站点
        
        Args:
            main_domain: 主域名
            
        Returns:
            QuerySet[Site]: 站点查询集
        """
        try:
            return Site.objects.filter(main_domain=main_domain).order_by('name')
        except Exception as e:
            logger.error(f"获取站群站点失败: {e}")
            return Site.objects.none()
    
    def create_or_get_site(self, site_config: Dict[str, str]) -> Dict[str, Any]:
        """
        创建或获取站点对象
        
        Args:
            site_config: 站点配置字典，包含name, domain, start_url, description
            
        Returns:
            包含站点对象和操作结果的字典
        """
        try:
            # 检查站点是否已存在
            existing_site = Site.objects.filter(
                domain=site_config['domain']
            ).first()
            
            if existing_site:
                return {
                    'success': True,
                    'site': existing_site,
                    'created': False,
                    'message': f"站点已存在: {existing_site.name}"
                }
            
            # 创建新站点
            site = Site.objects.create(
                name=site_config['name'],
                domain=site_config['domain'],
                start_url=site_config['start_url'],
                description=site_config.get('description', ''),
                is_active=True
            )
            
            return {
                'success': True,
                'site': site,
                'created': True,
                'message': f"站点创建成功: {site.name}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"站点创建失败: {str(e)}"
            }
    
    def step01_create_site(self, site_config: Dict[str, str]) -> Dict[str, Any]:
        """
        步骤1：站点深度初始化
        基于01_create_site.py的deep_initialize_site函数
        
        Args:
            site_config: 站点配置字典
            
        Returns:
            初始化结果字典
        """
        # 首先创建或获取站点
        site_result = self.create_or_get_site(site_config)
        if not site_result['success']:
            return site_result
        
        target_site = site_result['site']
        
        try:
            # 解析域名
            parsed_url = urlparse(target_site.start_url)
            domain = parsed_url.netloc
            
            # 移除www前缀获取基础域名
            if domain.startswith('www.'):
                base_domain = domain[4:]
            else:
                base_domain = domain
            
            print(f"🔄 正在创建首页SiteMap记录: {target_site.start_url}")
            
            # 为首页URL创建SiteMap记录
            homepage_sitemap = self.site_map_service.create_or_get_sitemap(
                site=target_site,
                url=target_site.start_url,
                title=f"{target_site.name}首页"
            )
            
            print(f"🔄 正在分析站点结构: {target_site.start_url}")
            
            # 通过SiteMap获取HTML内容并进行分析
            html_content = homepage_sitemap.get_html_content()
            if not html_content:
                return {
                    'success': False,
                    'error': f"无法获取首页HTML内容: {target_site.start_url}",
                    'site': target_site
                }
            
            # 使用新的HTML分析方法
            analysis_result = self.url_analysis_service.extract_and_classify_urls_from_html(
                html_content, target_site.start_url, base_domain
            )
            
            if not analysis_result.get('success'):
                return {
                    'success': False,
                    'error': f"URL分析失败: {analysis_result.get('error')}",
                    'site': target_site
                }
            
            print(f"✅ 站点结构分析完成")
            
            # 获取分类结果
            classifications = analysis_result.get('classifications', {})
            statistics = analysis_result.get('statistics', {})
            
            # 创建当前域名的站点地图
            site_map_result = None
            current_domain_urls = self.url_analysis_service.get_current_domain_urls(classifications)
            if current_domain_urls:
                print(f"🔄 正在创建站点地图: {len(current_domain_urls)} 个URL")
                site_map_result = self.site_map_service.create_site_maps_from_urls(
                    target_site, current_domain_urls
                )
                print(f"✅ 站点地图创建完成")
            
            # 发现和创建子站点
            sub_sites_result = None
            subdomain_urls = self.url_analysis_service.get_subdomain_urls(classifications)
            if subdomain_urls:
                print(f"🔄 正在发现子站点: {len(subdomain_urls)} 个子域名")
                sub_sites_result = self.site_map_service.discover_and_create_sub_sites(
                    target_site, subdomain_urls
                )
                print(f"✅ 子站点发现完成")
            
            # 更新站点描述
            description_parts = [
                f"深度初始化完成 - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
                f"发现链接: {statistics.get('total_urls', 0)} 个",
                f"站点地图: {site_map_result.get('created_count', 0) if site_map_result else 0} 个"
            ]
            
            target_site.description = "; ".join(description_parts)
            target_site.save()
            
            return {
                'success': True,
                'site': target_site,
                'homepage_sitemap': homepage_sitemap,
                'analysis_result': analysis_result,
                'site_map_result': site_map_result,
                'sub_sites_result': sub_sites_result,
                'statistics': statistics
            }
            
        except KeyboardInterrupt:
            return {
                'success': False,
                'error': "站点深度初始化被用户中断",
                'site': target_site,
                'interrupted': True
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"站点深度初始化失败: {str(e)}",
                'site': target_site
            }
