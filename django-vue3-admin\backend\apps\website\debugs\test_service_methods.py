#!/usr/bin/env python
# -*- coding: utf-8 -*-

from base import init_debug
init_debug()

from apps.website.service.site_map_service import SiteMapService

def test_methods():
    service = SiteMapService()
    
    print("Testing SiteMapService methods:")
    print(f"step02_2_apply_rules_to_sitemap: {hasattr(service, 'step02_2_apply_rules_to_sitemap')}")
    print(f"step03_update_content_type_by_ai: {hasattr(service, 'step03_update_content_type_by_ai')}")
    print(f"auto_generate_url_rules: {hasattr(service, 'auto_generate_url_rules')}")
    
    # 列出所有方法
    methods = [method for method in dir(service) if not method.startswith('_')]
    print(f"\nAll public methods: {methods}")

if __name__ == "__main__":
    test_methods()