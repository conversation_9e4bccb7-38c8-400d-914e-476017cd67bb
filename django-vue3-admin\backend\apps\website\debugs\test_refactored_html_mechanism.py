# -*- coding: utf-8 -*-
"""
测试重构后的HTML获取机制
验证统一的HTML获取和缓存是否正常工作
"""

from base import init_debug
init_debug()

from apps.website.service.integrated_crawler_service import IntegratedCrawlerService
from apps.website.service.site_map_service import SiteMapService
from apps.website.service.url_analysis_service_v2 import URLAnalysisServiceV2
from apps.website.models import Site, SiteMap

def test_create_or_get_sitemap():
    """测试create_or_get_sitemap方法"""
    print("🧪 测试create_or_get_sitemap方法...")
    
    # 创建一个测试站点
    test_site_config = {
        "name": "测试站点-重构验证",
        "domain": "test-refactor.example.com",
        "start_url": "https://test-refactor.example.com/",
        "description": "用于测试重构后HTML获取机制的测试站点"
    }
    
    service = IntegratedCrawlerService()
    site_result = service.create_or_get_site(test_site_config)
    
    if not site_result['success']:
        print(f"❌ 创建测试站点失败: {site_result['error']}")
        return False
    
    test_site = site_result['site']
    print(f"✅ 创建测试站点: {test_site.name} (ID: {test_site.id})")
    
    # 测试SiteMapService.create_or_get_sitemap方法
    site_map_service = SiteMapService()
    
    # 第一次调用：创建新记录
    sitemap1 = site_map_service.create_or_get_sitemap(
        site=test_site,
        url="https://test-refactor.example.com/test-page",
        title="测试页面"
    )
    
    print(f"✅ 创建SiteMap记录: {sitemap1.url} (ID: {sitemap1.id})")
    print(f"   标题: {sitemap1.title}")
    print(f"   unique_id: {sitemap1.unique_id}")
    
    # 第二次调用：获取现有记录
    sitemap2 = site_map_service.create_or_get_sitemap(
        site=test_site,
        url="https://test-refactor.example.com/test-page",
        title="更新的标题"
    )
    
    print(f"✅ 获取现有SiteMap记录: {sitemap2.url} (ID: {sitemap2.id})")
    print(f"   标题是否相同: {sitemap1.id == sitemap2.id}")
    
    # 清理测试数据
    SiteMap.objects.filter(site_id=test_site.id).delete()
    test_site.delete()
    print("🧹 清理测试数据完成")
    
    return True

def test_url_analysis_new_method():
    """测试新的URL分析方法"""
    print("\n🧪 测试新的URL分析方法...")
    
    url_service = URLAnalysisServiceV2()
    
    # 测试HTML内容
    test_html = """
    <html>
    <head><title>测试页面</title></head>
    <body>
        <a href="/page1">页面1</a>
        <a href="/page2">页面2</a>
        <a href="https://external.com">外部链接</a>
    </body>
    </html>
    """
    
    # 测试新的方法
    result = url_service.extract_and_classify_urls_from_html(
        html_content=test_html,
        base_url="https://test.example.com/",
        base_domain="test.example.com"
    )
    
    if result.get('success'):
        print("✅ 新方法extract_and_classify_urls_from_html工作正常")
        print(f"   发现链接数量: {result.get('statistics', {}).get('total_urls', 0)}")
        print(f"   HTML大小: {result.get('html_size', 0)} 字符")
    else:
        print(f"❌ 新方法测试失败: {result.get('error')}")
        return False
    
    return True

def main():
    """主测试函数"""
    print("🚀 开始测试重构后的HTML获取机制")
    print("=" * 60)
    
    # 测试1：SiteMapService的新方法
    test1_success = test_create_or_get_sitemap()
    
    # 测试2：URLAnalysisServiceV2的新方法
    test2_success = test_url_analysis_new_method()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 测试结果总结:")
    print(f"   create_or_get_sitemap: {'✅ 通过' if test1_success else '❌ 失败'}")
    print(f"   extract_and_classify_urls_from_html: {'✅ 通过' if test2_success else '❌ 失败'}")
    
    if test1_success and test2_success:
        print("\n🎉 所有测试通过！重构成功")
        print("💡 建议：现在可以测试完整的集成流程")
    else:
        print("\n⚠️  部分测试失败，需要检查实现")

if __name__ == "__main__":
    main()