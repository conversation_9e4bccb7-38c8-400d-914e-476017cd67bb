
"""
站点地图服务
专注于站点地图创建和子站点管理
"""

import logging
import json
import re
import signal
import time
from typing import Dict, List, Set, Tuple, Optional, Any
from urllib.parse import urlparse
from django.db import transaction
from django.db.models import Q
from django.utils import timezone

from apps.website.models import Site, SiteMap, Html
from apps.website.enums import WebsiteContentType
from apps.website.utils.async_utils import auto_sync_async_method
from .ai_service import ai_service

logger = logging.getLogger(__name__)


class InterruptHandler:
    """中断处理器 - 用于优雅处理KeyboardInterrupt"""
    
    def __init__(self):
        self.interrupted = False
        self.original_handler = None
    
    def __enter__(self):
        self.original_handler = signal.signal(signal.SIGINT, self._signal_handler)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.original_handler:
            signal.signal(signal.SIGINT, self.original_handler)
    
    def _signal_handler(self, signum, frame):
        print("\n⚠️  收到中断信号，正在优雅停止...")
        self.interrupted = True
    
    def check_interrupted(self):
        """检查是否被中断"""
        if self.interrupted:
            raise KeyboardInterrupt("用户中断了执行")


class SiteMapService:
    """站点地图服务"""
    
    def __init__(self):
        """初始化服务"""
        pass
    
    def create_site_maps_from_urls(self, site: Site, url_list: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        从URL列表创建SiteMap记录
        
        Args:
            site: 站点对象
            url_list: URL信息列表，格式: [{"url": "链接地址", "title": "标题", "type": "link|resource"}]
            
        Returns:
            创建结果字典
        """
        try:
            logger.info(f"开始为站点 {site.name} 创建站点地图，共 {len(url_list)} 个URL")
            
            created_count = 0
            skipped_count = 0
            error_count = 0
            
            with transaction.atomic():
                for url_info in url_list:
                    try:
                        url = url_info.get('url', '')
                        title = url_info.get('title', '')
                        url_type = url_info.get('type', 'link')
                        
                        if not url:
                            continue
                        
                        # 检查是否已存在
                        if SiteMap.objects.filter(site_id=site.id, url=url).exists():
                            skipped_count += 1
                            continue
                        
                        # 解析URL信息
                        parsed_url = urlparse(url)
                        
                        # 创建SiteMap记录
                        site_map = SiteMap.objects.create(
                            site_id=site.id,
                            url=url,
                            title=title or '无标题',
                            id_path=parsed_url.path,  # 使用id_path字段
                            content_type=WebsiteContentType.UNKNOWN,  # 默认为unknown
                            update_frequency='weekly',
                            last_crawl_time=timezone.now()
                        )
                        
                        created_count += 1
                        
                    except Exception as e:
                        logger.error(f"创建SiteMap记录失败 {url_info}: {e}")
                        error_count += 1
                        continue
            
            logger.info(f"站点地图创建完成：创建 {created_count} 个，跳过 {skipped_count} 个，错误 {error_count} 个")
            
            return {
                'success': True,
                'site_id': site.pk,
                'created_count': created_count,
                'skipped_count': skipped_count,
                'error_count': error_count,
                'total_processed': len(url_list)
            }
            
        except Exception as e:
            logger.error(f"批量创建站点地图失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'site_id': site.pk
            }
    
    def discover_and_create_sub_sites(self, parent_site: Site, subdomain_urls: List[Dict[str, str]]) -> Dict[str, Any]:
        """
        发现并创建子站点
        
        Args:
            parent_site: 父站点
            subdomain_urls: 子域名URL列表
            
        Returns:
            创建结果字典
        """
        try:
            logger.info(f"开始为站点 {parent_site.name} 创建子站点，共 {len(subdomain_urls)} 个子域名URL")
            
            created_sites = []
            skipped_sites = []
            error_count = 0
            
            # 按域名分组
            domain_groups = {}
            for url_info in subdomain_urls:
                url = url_info.get('url', '')
                if not url:
                    continue
                    
                try:
                    parsed_url = urlparse(url)
                    domain = parsed_url.netloc
                    
                    if domain not in domain_groups:
                        domain_groups[domain] = []
                    domain_groups[domain].append(url_info)
                    
                except Exception as e:
                    logger.error(f"解析子域名URL失败 {url}: {e}")
                    error_count += 1
                    continue
            
            # 为每个子域名创建站点
            with transaction.atomic():
                for domain, urls in domain_groups.items():
                    try:
                        # 检查是否已存在
                        if Site.objects.filter(domain=domain).exists():
                            skipped_sites.append(domain)
                            continue
                        
                        # 生成站点名称
                        site_name = self._generate_sub_site_name(domain, parent_site.name)
                        
                        # 选择一个URL作为起始URL
                        start_url = urls[0].get('url', '')
                        
                        # 创建子站点
                        sub_site = Site.objects.create(
                            name=site_name,
                            domain=domain,
                            start_url=start_url,
                            parent_site=parent_site,
                            is_active=True,
                            url_matching_rules=[],
                            content_section_detection_rules=[],
                            description=f"自动创建的子站点，发现来源: {parent_site.start_url}，发现URL数量: {len(urls)}，创建方法: subdomain_discovery"
                        )
                        
                        # 为子站点创建SiteMap记录
                        self.create_site_maps_from_urls(sub_site, urls)
                        
                        created_sites.append({
                            'site_id': sub_site.pk,
                            'name': sub_site.name,
                            'domain': domain,
                            'start_url': start_url,
                            'urls_count': len(urls)
                        })
                        
                        logger.info(f"创建子站点成功: {site_name} ({domain})")
                        
                    except Exception as e:
                        logger.error(f"创建子站点失败 {domain}: {e}")
                        error_count += 1
                        continue
            
            logger.info(f"子站点创建完成：创建 {len(created_sites)} 个，跳过 {len(skipped_sites)} 个，错误 {error_count} 个")
            
            return {
                'success': True,
                'parent_site_id': parent_site.pk,
                'created_sites': created_sites,
                'skipped_sites': skipped_sites,
                'error_count': error_count,
                'total_domains': len(domain_groups)
            }
            
        except Exception as e:
            logger.error(f"发现和创建子站点失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'parent_site_id': parent_site.pk
            }
    
    
    def _generate_sub_site_name(self, domain: str, parent_name: str) -> str:
        """
        生成子站点名称
        
        Args:
            domain: 子域名
            parent_name: 父站点名称
            
        Returns:
            生成的站点名称
        """
        # 提取子域名前缀
        if '.' in domain:
            subdomain_prefix = domain.split('.')[0]
            
            # 常见的子域名映射
            subdomain_mapping = {
                'www': '主站',
                'news': '新闻',
                'info': '信息',
                'service': '服务',
                'gov': '政务',
                'admin': '管理',
                'api': 'API',
                'mobile': '移动端',
                'wap': 'WAP',
                'mail': '邮件',
                'ftp': 'FTP',
                'blog': '博客',
                'forum': '论坛',
                'bbs': '论坛',
                'shop': '商城',
                'store': '商店'
            }
            
            # 获取子域名的中文名称
            subdomain_name = subdomain_mapping.get(subdomain_prefix, subdomain_prefix)
            
            return f"{parent_name} - {subdomain_name}站"
        else:
            return f"{parent_name} - 子站点"
    
    


    @auto_sync_async_method
    def create_or_update_sitemap_by_url(self, site, url: str, title: Optional[str] = None, last_modified: Optional[str] = None, html_content: Optional[str] = None) -> tuple[SiteMap, bool]:
        """
        通过URL创建或更新站点地图条目
        如果URL已存在，则更新title和html_content（如果提供了新值）
        注意：last_modified参数保留用于兼容性，但不会存储到数据库
        """
        defaults = {}
        if title:
            # 限制title长度，避免数据库字段溢出
            defaults['title'] = title[:200] if len(title) > 200 else title
        if html_content is not None:
            # 保存HTML内容
            defaults['html_content'] = html_content
        # 移除last_modified字段，因为SiteMap模型中没有这个字段
        # 可以使用updated_at字段来跟踪更新时间（自动更新）
        
        sitemap, created = SiteMap.objects.update_or_create(
            site_id=site.id,
            url=url,
            defaults=defaults
        )
        return sitemap, created

    def apply_rules_to_sitemap(self, site: Site) -> Dict[str, Any]:
        """
        应用URL匹配规则到站点地图
        
        Args:
            site: 站点对象
            
        Returns:
            规则应用结果字典
        """
        try:
            # 验证站点是否有规则
            rules = site.url_matching_rules
            if not rules:
                return {
                    'success': False,
                    'error': f"站点 {site.name} 没有配置URL匹配规则",
                    'site': site
                }
            
            # 创建内容类型匹配器
            from ..service.content_type_matcher import ContentTypeMatcher
            matcher = ContentTypeMatcher(rules)
            
            # 获取该站点的所有SiteMap记录
            sitemaps = SiteMap.objects.filter(site_id=site.id)
            
            if sitemaps.count() == 0:
                return {
                    'success': False,
                    'error': f"站点 {site.name} 没有SiteMap记录",
                    'site': site
                }
            
            # 统计分类前的状态
            before_stats = {}
            for sitemap in sitemaps:
                content_type = sitemap.content_type_rule or 'unknown'
                before_stats[content_type] = before_stats.get(content_type, 0) + 1
            
            # 批量重新分类
            updated_count = 0
            classification_changes = []
            
            with transaction.atomic():
                for sitemap in sitemaps:
                    old_type = sitemap.content_type_rule
                    new_type = matcher.match_content_type(sitemap.url)
                    
                    if old_type != new_type:
                        sitemap.content_type_rule = new_type
                        sitemap.save(update_fields=['content_type_rule'])
                        updated_count += 1
                        
                        classification_changes.append({
                            'url': sitemap.url,
                            'old_type': old_type,
                            'new_type': new_type
                        })
            
            # 统计分类后的状态
            sitemaps_after = SiteMap.objects.filter(site_id=site.id)
            after_stats = {}
            for sitemap in sitemaps_after:
                content_type = sitemap.content_type_rule or 'unknown'
                after_stats[content_type] = after_stats.get(content_type, 0) + 1
            
            return {
                'success': True,
                'site': site,
                'rules_count': len(rules),
                'total_sitemaps': sitemaps.count(),
                'updated_count': updated_count,
                'before_stats': before_stats,
                'after_stats': after_stats,
                'classification_changes': classification_changes[:10]  # 只返回前10个变更
            }
            
        except Exception as e:
            logger.error(f"应用规则分类失败: {e}")
            return {
                'success': False,
                'error': f"应用规则分类失败: {str(e)}",
                'site': site
            }

    @staticmethod
    def get_content_section_sitemaps(site: Site, verbose: bool = True, only_without_match_rule: bool = False) -> List[SiteMap]:
        """
        获取指定站点的所有content section类型的SiteMap对象

        Args:
            site: Site对象
            verbose: 是否输出详细日志
            only_without_match_rule: 只返回不在Column表的SiteMap

        Returns:
            List[SiteMap]: content_section类型的SiteMap对象列表，按创建时间排序
        """
        if verbose:
            logger.info(f"开始查找站点 {site.name} 的content section列表")
            print(f"   检查站点: {site.name} ({site.domain})")

        # 先查看该站点有什么类型的数据
        all_sections = SiteMap.objects.filter(site_id=site.id)
        if verbose:
            content_types_ai = all_sections.values_list('content_type_ai', flat=True).distinct()
            content_types = all_sections.values_list('content_type', flat=True).distinct()
            print(f"   该站点的content_type_ai: {list(content_types_ai)}")
            print(f"   该站点的content_type: {list(content_types)}")

        # 基础查询条件
        query = SiteMap.objects.filter(
            site_id=site.id,
            content_type_ai='content_section'
        ).exclude(url__isnull=True).exclude(url__exact='')

        # 如果需要过滤掉已有Column的SiteMap
        if only_without_match_rule:
            from apps.website.model.column import Column
            existing_column_unique_ids = Column.objects.filter(
                site_id=site.id
            ).values_list('unique_id', flat=True)
            query = query.exclude(unique_id__in=existing_column_unique_ids)

        # 执行查询并排序
        sitemaps_list = list(query.order_by('created_at'))

        if verbose:
            if only_without_match_rule:
                print(f"   找到 {len(sitemaps_list)} 个未关联Column的content section")
            else:
                print(f"   找到 {len(sitemaps_list)} 个AI分类的content section")

        return sitemaps_list

    def getNoHtmlCacheSitemaps(self, site_id: int, max_records: Optional[int] = None) -> List[SiteMap]:
        """
        获取指定站点中没有HTML缓存的SiteMap记录

        Args:
            site_id: 站点ID
            max_records: 最大记录数，None表示不限制

        Returns:
            List[SiteMap]: 没有HTML缓存的SiteMap对象列表
        """
        try:
            logger.info(f"开始查找站点 {site_id} 中没有HTML缓存的SiteMap记录")

            # 获取所有已有HTML缓存的unique_id列表
            cached_unique_ids = Html.objects.values_list('unique_id', flat=True)

            # 查询指定站点的SiteMap记录，排除已有缓存的记录
            queryset = SiteMap.objects.filter(site_id=site_id)

            # 排除已有HTML缓存的记录
            # 注意：需要处理unique_id为None的情况
            queryset = queryset.exclude(unique_id__in=cached_unique_ids)

            # 确保unique_id不为空（为空的记录也需要处理）
            # 这里不排除unique_id为None的记录，因为它们也需要抓取HTML

            # 按创建时间排序，确保结果一致性
            queryset = queryset.order_by('created_at')

            # 限制记录数量
            if max_records:
                queryset = queryset[:max_records]

            sitemaps_list = list(queryset)

            # 为没有unique_id的SiteMap生成unique_id
            updated_count = 0
            for sitemap in sitemaps_list:
                if not sitemap.unique_id:
                    sitemap.unique_id = sitemap.generate_unique_id()
                    sitemap.save(update_fields=['unique_id'])
                    updated_count += 1

            if updated_count > 0:
                logger.info(f"为 {updated_count} 个SiteMap记录生成了unique_id")

            logger.info(f"找到 {len(sitemaps_list)} 个没有HTML缓存的SiteMap记录")

            return sitemaps_list

        except Exception as e:
            logger.error(f"获取没有HTML缓存的SiteMap记录失败: {e}")
            return []
    
    def analyze_and_update_content_type(self, sitemap: SiteMap, site: Site) -> Dict[str, Any]:
        """
        分析并更新SiteMap的内容类型
        
        Args:
            sitemap: SiteMap对象
            site: 站点对象
            
        Returns:
            Dict: 分析结果
        """
        try:
            logger.info(f"开始分析SiteMap内容类型: {sitemap.url}")
            
            # 通过SiteMap获取HTML内容（利用缓存机制）
            html_content = sitemap.get_html_content()
            if not html_content:
                return {'success': False, 'error': 'HTML内容不可用', 'url': sitemap.url}
            
            # 调用AI分析，传递站点域名作为user_id
            ai_result = ai_service.analyze_url_content_type(
                sitemap.url, html_content=html_content, user_id=site.domain
            )
            
            # 检查AI结果的有效性
            if not isinstance(ai_result, dict):
                logger.error(f"AI服务返回非字典类型: {type(ai_result)} - {ai_result}")
                ai_result = {'success': False, 'error': f'AI服务返回无效类型: {type(ai_result)}'}
            
            # 更新数据库
            if ai_result.get('success'):
                content_type = ai_result.get('content_type')
                sitemap.content_type_ai = content_type
                sitemap.save(update_fields=['content_type_ai'])
                logger.info(f"AI分析成功并更新: {sitemap.url} -> {content_type}")
                
                # 添加处理结果信息
                ai_result.update({
                    'url': sitemap.url,
                    'html_content_length': len(html_content),
                    'update_success': True,
                    'sitemap_id': sitemap.id
                })
            else:
                logger.error(f"AI分析失败: {ai_result.get('error')}")
                ai_result.update({
                    'url': sitemap.url,
                    'update_success': False,
                    'sitemap_id': sitemap.id
                })
            
            return ai_result
            
        except Exception as e:
            logger.error(f"分析SiteMap内容类型失败 {sitemap.url}: {e}")
            return {
                'success': False, 
                'error': str(e), 
                'url': sitemap.url,
                'update_success': False,
                'sitemap_id': sitemap.id
            }
    
    def batch_analyze_content_types(self, sitemaps: List[SiteMap], site: Site) -> Dict[str, Any]:
        """
        批量分析SiteMap的内容类型
        
        Args:
            sitemaps: SiteMap对象列表
            site: 站点对象
            
        Returns:
            Dict: 批量处理结果
        """
        try:
            logger.info(f"开始批量分析 {len(sitemaps)} 个SiteMap的内容类型")
            
            success_count = 0
            failed_count = 0
            results = []
            
            for sitemap in sitemaps:
                result = self.analyze_and_update_content_type(sitemap, site)
                results.append(result)
                
                if result.get('success'):
                    success_count += 1
                else:
                    failed_count += 1
            
            logger.info(f"批量分析完成: 成功 {success_count} 个, 失败 {failed_count} 个")
            
            return {
                'success': True,
                'total_count': len(sitemaps),
                'success_count': success_count,
                'failed_count': failed_count,
                'results': results
            }
            
        except Exception as e:
            logger.error(f"批量分析SiteMap内容类型失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'total_count': len(sitemaps),
                'success_count': 0,
                'failed_count': len(sitemaps)
            }
    
    def create_or_get_sitemap(self, site: Site, url: str, title: str = None) -> SiteMap:
        """
        创建或获取指定URL的SiteMap记录
        
        Args:
            site: 站点对象
            url: URL地址
            title: 页面标题，如果不提供则使用默认值
            
        Returns:
            SiteMap: 创建或获取的SiteMap对象
        """
        try:
            # 先尝试获取现有记录
            try:
                sitemap = SiteMap.objects.get(site_id=site.id, url=url)
                logger.info(f"获取现有SiteMap记录: {url}")
                
                # 如果提供了新标题且当前标题为默认值，则更新标题
                if title and sitemap.title in ['无标题', '']:
                    sitemap.title = title
                    sitemap.save(update_fields=['title'])
                    logger.info(f"更新SiteMap标题: {title}")
                
                return sitemap
                
            except SiteMap.DoesNotExist:
                # 记录不存在，创建新记录
                parsed_url = urlparse(url)
                
                sitemap = SiteMap.objects.create(
                    site_id=site.id,
                    url=url,
                    title=title or '无标题',
                    id_path=parsed_url.path,
                    content_type=WebsiteContentType.UNKNOWN,
                    update_frequency='weekly',
                    last_crawl_time=timezone.now()
                )
                
                # 生成unique_id
                sitemap.unique_id = sitemap.generate_unique_id()
                sitemap.save(update_fields=['unique_id'])
                
                logger.info(f"创建新SiteMap记录: {url}")
                return sitemap
                
        except Exception as e:
            logger.error(f"创建或获取SiteMap记录失败 {url}: {e}")
            raise
    
    def auto_generate_url_rules(self, site: Site) -> Dict[str, Any]:
        """
        自动生成URL分类规则
        
        Args:
            site: 站点对象
            
        Returns:
            规则生成结果
        """
        try:
            # 延迟导入避免循环依赖
            from .ai_services.dify_service import DifyService
            dify_service = DifyService()
            
            # 获取站点的所有URL
            sitemaps = SiteMap.objects.filter(site_id=site.id).order_by('url')
            
            if sitemaps.count() == 0:
                return {
                    'success': False,
                    'error': f"站点 {site.name} 没有SiteMap数据"
                }
            
            # 构建URL文本（模拟Markdown表格格式）
            urls_text_lines = [
                f"# {site.domain} 域名URL列表",
                "",
                f"总计：{sitemaps.count()} 个链接",
                "",
                "| 链接地址 | 标题 |",
                "|----------|------|"
            ]
            
            for sitemap in sitemaps:
                title = (sitemap.title or "无标题").replace('|', '\\|')  # 转义管道符
                urls_text_lines.append(f"| {sitemap.url} | {title} |")
            
            urls_text = '\n'.join(urls_text_lines)
            
            # 调用Dify服务生成规则
            result = dify_service.analyze_url_classification_rules(urls_text)
            
            if result.get('success'):
                rules = result.get('rules', [])
                single_rule = result.get('single_rule', {})
                
                # 验证规则有效性
                validation_result = self._validate_rule(single_rule)
                
                if validation_result['valid']:
                    # 自动保存规则到数据库
                    save_result = self._save_rules_to_database(site, rules)
                    
                    if save_result['success']:
                        return {
                            'success': True,
                            'rules': rules,
                            'single_rule': single_rule,
                            'saved': True,
                            'message': f"成功生成并保存规则: {single_rule.get('pattern')} -> {single_rule.get('content_type')}",
                            'save_details': save_result
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"规则生成成功但保存失败: {save_result['error']}",
                            'rules': rules
                        }
                else:
                    return {
                        'success': False,
                        'error': f"规则验证失败: {validation_result['reason']}",
                        'rules': rules
                    }
            else:
                return {
                    'success': False,
                    'error': f"AI规则生成失败: {result.get('error')}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"自动生成规则异常: {str(e)}"
            }
    
    def _validate_rule(self, rule: Dict[str, str]) -> Dict[str, Any]:
        """验证生成的规则是否有效"""
        try:
            pattern = rule.get('pattern')
            content_type = rule.get('content_type')
            
            # 基本格式检查
            if not pattern or not content_type:
                return {'valid': False, 'reason': '规则缺少必要字段'}
            
            # 检查内容类型是否有效
            valid_types = [choice[0] for choice in WebsiteContentType.choices]
            if content_type not in valid_types:
                return {'valid': False, 'reason': f'无效的内容类型: {content_type}'}
            
            # 检查正则表达式是否有效
            try:
                re.compile(pattern)
            except re.error as e:
                return {'valid': False, 'reason': f'无效的正则表达式: {e}'}
            
            # 简单的规则质量检查
            if len(pattern) < 5:
                return {'valid': False, 'reason': '规则过于简单，可能不够准确'}
            
            return {'valid': True, 'reason': '规则验证通过'}
            
        except Exception as e:
            return {'valid': False, 'reason': f'验证过程异常: {e}'}
    
    def _save_rules_to_database(self, site: Site, rules: list) -> Dict[str, Any]:
        """将规则保存到数据库"""
        try:
            # 备份现有规则
            original_rules = site.url_matching_rules.copy() if site.url_matching_rules else []
            
            # 合并新规则（新规则添加到开头，优先级更高）
            updated_rules = rules + original_rules
            
            # 更新站点规则
            site.url_matching_rules = updated_rules
            site.save()
            
            return {
                'success': True,
                'original_count': len(original_rules),
                'new_count': len(rules),
                'total_count': len(updated_rules)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def step02_2_apply_rules_to_sitemap(self, site: Site) -> Dict[str, Any]:
        """
        步骤2-2：应用规则分类
        基于02_2_applay_rules_to_sitemap.py的apply_rules_to_sitemap函数
        
        Args:
            site: 站点对象
            
        Returns:
            规则应用结果字典
        """
        print(f"\n📋 步骤2-2：应用规则分类")
        
        max_attempts = 3
        attempt = 0
        result: Dict[str, Any] = {
            'success': False,
            'error': "初始化状态",
            'site': site
        }
        
        while attempt < max_attempts:
            attempt += 1
            print(f"\n🔄 第{attempt}次尝试应用规则...")
            
            # 检查站点是否有规则
            if not site.url_matching_rules:
                print("❌ 站点没有URL匹配规则，尝试自动生成...")
                rule_result = self.auto_generate_url_rules(site)
                if not rule_result.get('success'):
                    print(f"❌ 自动生成规则失败: {rule_result.get('error')}")
                    if attempt == max_attempts:
                        raise Exception(
                            "❌ 无法生成有效的URL匹配规则：\n"
                            f"- 连续{max_attempts}次生成规则都失败\n"
                            f"- 错误信息: {rule_result.get('error')}\n"
                            "- 请检查URL模式或考虑手动创建规则"
                        )
                    continue
                
                # 重新获取站点对象以获取新规则
                site.refresh_from_db()
            
            # 应用规则
            result = self.apply_rules_to_sitemap(site)
            if not result.get('success'):
                print(f"❌ 应用规则失败: {result.get('error')}")
                if attempt == max_attempts:
                    raise Exception(
                        "❌ 无法应用URL匹配规则：\n"
                        f"- 连续{max_attempts}次应用规则都失败\n"
                        f"- 错误信息: {result.get('error')}\n"
                        "- 请检查规则配置或考虑手动修复"
                    )
                continue
            
            # 获取分类统计
            after_stats = result.get('after_stats', {})
            total_records = result.get('total_sitemaps', 0)
            unknown_count = after_stats.get('unknown', 0)
            
            # 检查是否所有记录都是unknown
            if unknown_count == total_records and total_records > 0:
                if attempt == max_attempts:
                    raise Exception(
                        "❌ 无法生成有效的URL匹配规则：\n"
                        f"- 连续{max_attempts}次生成的规则都无法正确分类URL\n"
                        "- 所有URL都被分类为unknown\n"
                        "- 请检查URL模式或考虑手动创建规则"
                    )
                
                print(f"❌ 所有{total_records}个URL都被分类为unknown，尝试重新生成规则...")
                # 重新生成规则
                rule_result = self.auto_generate_url_rules(site)
                if not rule_result.get('success'):
                    print(f"❌ 重新生成规则失败: {rule_result.get('error')}")
                    if attempt == max_attempts:
                        raise Exception(
                            "❌ 无法生成有效的URL匹配规则：\n"
                            f"- 连续{max_attempts}次重新生成规则都失败\n"
                            f"- 错误信息: {rule_result.get('error')}\n"
                            "- 请检查URL模式或考虑手动创建规则"
                        )
                    continue

                print(f"✅ 重新生成规则成功: {rule_result.get('message', '规则已更新')}")
                # 重新获取站点对象以获取新规则
                site.refresh_from_db()
                continue
            
            # 规则应用成功，打印结果
            print("✅ 规则应用成功")
            print(f"   规则数量: {result.get('rules_count', 0)} 条")
            print(f"   更新记录: {result.get('updated_count', 0)} 个")
            print(f"   分类统计: {after_stats}")
            
            # 如果有分类成功的记录，返回结果
            return result
        
        # 这行代码实际上不会执行到，因为最后一次失败会抛出异常
        return result
    
    def step03_update_content_type_by_ai(self, site: Site, limit: int = 50) -> Dict[str, Any]:
        """
        步骤3：AI内容分类
        基于03_update_content_type_by_ai.py的update_sitemap_content_type_by_ai函数
        
        Args:
            site: 站点对象
            limit: 本次处理的最大条目数
            
        Returns:
            AI分类结果字典
        """
        try:
            # 筛选出规则无法识别且AI未判定过的SiteMap记录
            sitemaps_to_process = SiteMap.objects.filter(
                Q(site_id=site.id) &
                ~Q(content_type_rule=WebsiteContentType.CONTENT.value) &
                (Q(content_type_ai__isnull=True) | Q(content_type_ai=''))
            )[:limit]
            
            total_count = sitemaps_to_process.count()
            
            if total_count == 0:
                return {
                    'success': True,
                    'site': site,
                    'message': "没有找到需要AI分析的SiteMap记录",
                    'processed_count': 0,
                    'success_count': 0,
                    'failed_count': 0
                }
            
            # 使用SiteMapService进行AI分析
            
            success_count = 0
            failed_count = 0
            results = []
            interrupted = False
            
            # 使用中断处理器
            with InterruptHandler() as interrupt_handler:
                for i, sitemap in enumerate(sitemaps_to_process, 1):
                    # 检查是否被中断
                    try:
                        interrupt_handler.check_interrupted()
                    except KeyboardInterrupt:
                        print(f"\n⚠️  在处理第 {i}/{total_count} 个URL时被中断")
                        interrupted = True
                        break
                    
                    print(f"🔄 处理 {i}/{total_count}: {sitemap.url}")
                    
                    # 使用SiteMapService进行AI分析和更新
                    result = self.analyze_and_update_content_type(sitemap, site)
                    
                    if result.get('success') and result.get('update_success'):
                        success_count += 1
                        results.append({
                            'url': sitemap.url,
                            'status': 'success',
                            'content_type': result.get('content_type'),
                            'new_links_count': result.get('new_links_count', 0)
                        })
                        print(f"✅ 成功: {result.get('content_type')}")
                    else:
                        failed_count += 1
                        results.append({
                            'url': sitemap.url,
                            'status': 'failed',
                            'error': result.get('error', '未知错误')
                        })
                        print(f"❌ 失败: {result.get('error', '未知错误')}")
                    
                    # 添加短暂延迟，给中断信号处理留出时间
                    time.sleep(0.1)
            
            # 如果被中断，在结果中标记
            result_dict = {
                'success': True,
                'site': site,
                'processed_count': success_count + failed_count,
                'success_count': success_count,
                'failed_count': failed_count,
                'results': results[:10]  # 只返回前10个结果
            }
            
            if interrupted:
                result_dict['interrupted'] = True
                result_dict['message'] = f"处理被用户中断，已完成 {success_count + failed_count}/{total_count} 个URL"
            
            return result_dict
            
        except KeyboardInterrupt:
            return {
                'success': False,
                'error': "AI内容分类被用户中断",
                'site': site,
                'interrupted': True
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"AI内容分类失败: {str(e)}",
                'site': site
            }