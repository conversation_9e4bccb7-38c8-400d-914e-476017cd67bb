#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
生成内蒙古医科大学的AI配置

使用AI配置生成服务分析内蒙古医科大学网站
"""
from base import init_debug
init_debug()
# 只用相对路径导入base,初始化Django环境
from apps.website.models import ColumnRule, Site, SiteMap
from apps.website.service.site_map_service import SiteMapService
from apps.website.service.site_service import SiteService

from apps.website.service.column_rule_manager import ColumnRuleManager



def try_associate_all_pagination_rule(site:Site):
    """
    尝试使用规则和sitemap进行匹配,如果匹配成功, 则在column中创建一条记录
    """
    sitemaps=SiteMapService().get_content_section_sitemaps(site,only_without_match_rule=True)
    pagination_rules=ColumnRule.objects.filter(site_id=site.id)
    for sitemap in sitemaps:
        for pagination_rule in pagination_rules:
            column=ColumnRuleManager.try_associate_rule(sitemap,pagination_rule)
            if column is not None:
                print(f"✅ 关联分页规则成功: {pagination_rule.name} -> {sitemap.url}")
            else:
                print(f"❌ 关联分页规则失败: {pagination_rule.name} -> {sitemap.url}")



def try_generate_pagination_rule(site:Site):
    """
    尝试生成分页规则
    """
    sitemaps=SiteMapService().get_content_section_sitemaps(site,only_without_match_rule=True)
    # 从列表中pop一个
    sitemap=sitemaps.pop()
    column_rule=ColumnRuleManager.generate_pagination_rule(sitemap)
    if column_rule is None:
        print("❌ 生成分页规则失败")
        return 1
    print(column_rule)
    # 循环遍历剩余的
    for sitemap in sitemaps:
        # 尝试使用规则和sitemap进行匹配,如果匹配成功, 则在column中创建一条记录
        column=ColumnRuleManager.try_associate_rule(sitemap,column_rule)
        if column is not None:
            print(f"✅ 关联分页规则成功: {column_rule.name} -> {sitemap.url}")
        else:
            print(f"❌ 关联分页规则失败: {column_rule.name} -> {sitemap.url}")
    



def try_generate_pagination_rule_by_sitemap_url(sitemap_url:str):
    """
    尝试生成分页规则
    """
    sitemap=SiteMap.objects.filter(url=sitemap_url).first()
    if sitemap is None:
        print("❌ 未找到站点Map对象")
        return 1
    column_rule=ColumnRuleManager.get_column_rule(sitemap)
    if column_rule is not None:
        print("✅ 找到栏目规则")
        print(column_rule)
        return 0
    # 从列表中pop一个
    pagination_rule=ColumnRuleManager.generate_pagination_rule(sitemap)
    if pagination_rule is None:
        print("❌ 生成分页规则失败")
        return 1
    print(pagination_rule)
    # 循环遍历剩余的
    

def main():
    """主函数"""
    print("🚀 内蒙古医科大学AI配置生成")
    print("="*60)

    ColumnRuleManager.get_column_rule(sitemap)

   # 生成配置
    site=SiteService().find_site_by_domain_pattern('immu.edu.cn')
    if site is None:
        print("❌ 未找到站点")
        return 1
    # try_generate_pagination_rule_by_sitemap_url("https://www.immu.edu.cn/xxx/index.html")
    try_associate_all_pagination_rule(site)
    # try_generate_pagination_rule(site)


if __name__ == "__main__":
    import sys
    exit_code = main()
    sys.exit(exit_code)
