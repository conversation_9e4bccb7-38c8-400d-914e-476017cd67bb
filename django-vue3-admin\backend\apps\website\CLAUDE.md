# Website通用爬虫系统

## 系统概述
基于"人工少量操作 + AI关键特性判断 + Python通用执行"架构的通用网站爬虫平台。支持任意网站的智能内容抓取，通过AI自动分析网站结构和生成抓取规则，使用Playwright模拟真实用户行为进行通用化抓取。

## 核心架构特点
- **通用性**: 支持任意网站结构，不限于政府网站
- **智能化**: AI驱动的页面分析和规则生成
- **自动化**: 基于Playwright的通用翻页处理
- **可扩展**: 模块化设计，易于扩展新的网站类型

## 核心模型

### 数据模型结构
- **Site**: 网站基本信息，支持父子站点关系
- **SiteMap**: URL关联关系管理，记录页面类型和层级关系
- **Column**: 栏目页管理，包含分页抓取状态
- **ColumnRule**: 栏目规则配置，包含内容列表提取和分页规则
- **Content**: 最终内容页数据存储
- **ContentExtractionRule**: 内容页提取规则配置
- **Html**: 原始HTML缓存

### 关键枚举
- **CrawlScope**: 抓取范围（当前域名、发现子域名、站群全部、已知站点）
- **WebsiteContentType**: 内容类型（首页、栏目页、内容页、服务页等）
- **PaginationType**: 分页处理方式（浏览器自动化、直接HTTP、统一浏览器处理、无分页）
- **CrawlMode**: 抓取模式（深度抓取、增量抓取）

## 核心服务类

### 主要服务
- **SiteService**: 站点管理服务，负责站点创建和深度初始化
- **SiteMapService**: 站点地图服务，管理URL记录、页面分类和AI内容分析
- **ColumnService**: 栏目内容抓取服务，负责分页抓取和内容发现
- **ColumnPaginationRuleManager**: 栏目规则管理器，处理规则生成、测试、关联
- **ColumnBrowserPaginationHandler**: 浏览器分页处理器，使用Playwright进行真实翻页
- **ContentService**: 内容提取服务，提取标题、正文、时间等信息

### AI集成服务
- **AIService**: AI服务统一入口
- **DifyService**: Dify平台集成
- **CozeService**: 扣子平台集成

## 核心工作流程

### 完整抓取流程
1. **人工操作**: 通过后台管理创建目标网站Site，提供基本信息
2. **Python首抓**: 自动抓取首页所有链接，创建SiteMap记录
3. **AI内容分析**: 分析所有链接，确定内容链接的规则模式（占95%+的链接）
4. **Python标记**: 根据AI生成的规则，自动标记所有内容类型链接
5. **AI栏目识别**: 分析剩余未标记链接，判断是否为栏目页面
6. **Python栏目管理**: 标记识别出的栏目，建立Column记录
7. **AI规则生成**: 为每个栏目生成专用的翻页规则和内容提取规则
8. **Python+Playwright抓取**: 使用通用浏览器自动化进行翻页抓取
9. **智能抓取模式**: 根据需求选择深度抓取或增量抓取

### 抓取模式详解
- **深度抓取(DEEP_CRAWL)**: 基于crawled_pages继续抓取，从上次位置开始，适合补充历史内容
- **增量抓取(UPDATE_CRAWL)**: 从第一页开始，遇到重复内容立即停止，适合定期更新

### 支持的网站示例
- 政府网站：乌兰察布、锡林郭勒等
- 教育网站：内蒙古医科大学
- 新闻网站：习近平讲话数据库
- **理论上支持任意网站结构**

## 常用命令

### 测试运行
```bash
# 运行模块测试
python manage.py test apps.website

# 调试特定网站抓取
python apps/website/debugs/00_integrated_crawler_wlcb.py
```

### 数据库操作
```bash
# 迁移数据库
python manage.py makemigrations website
python manage.py migrate

# 清理测试数据
python apps/website/debugs/clear_migration_and_fake_migrat.py
```

## 技术栈
- **后端框架**: Django 4.x
- **异步处理**: asyncio, asyncio-utils
- **浏览器自动化**: Playwright
- **AI集成**: Dify, 扣子(Coze)
- **数据存储**: MySQL/PostgreSQL
- **配置管理**: JSON格式存储

## 系统实现状况

### ✅ 已完整实现的功能
- Site管理和SiteMap创建 (SiteService, SiteMapService)
- AI内容分析和规则生成 (AIContentAnalyzer, DifyService, CozeService)
- 内容类型匹配和标记 (ContentTypeMatcher)
- 栏目识别和规则管理 (ColumnService, ColumnRuleManager)
- Playwright通用翻页处理 (ColumnBrowserPaginationHandler)
- 增量和深度抓取模式 (CrawlMode枚举和实现)
- 完整的集成流程控制 (IntegratedCrawlerService)

### ⚠️ 当前已知问题
1. **分页解析稳定性**: 乌兰察布网站页码解析问题 (`column_browser_pagination_handler.py:_analyze_pagination()`)
2. **字段变更影响**: html_content字段移除的潜在影响需要验证
3. **错误处理**: 部分异常处理机制需要完善

### 🎯 下一步落地计划

#### 第一阶段：修复关键问题 (1-2周)
- 修复乌兰察布网站分页解析稳定性问题
- 验证html_content字段移除的影响范围
- 完善异常处理和错误恢复机制

#### 第二阶段：系统完善 (2-3周)
- 建立端到端测试流程
- 优化AI规则生成准确性
- 完善用户操作界面

#### 第三阶段：生产就绪 (3-4周)
- 性能监控和告警机制
- 批量网站测试验证
- 部署文档和运维指南

## 技术优势

### 通用性设计
- **统一浏览器处理**: 使用Playwright模拟真实用户操作，避免复杂的技术分析
- **AI驱动适配**: 自动识别网站特征，生成专用抓取规则
- **模块化架构**: 各组件职责清晰，易于维护和扩展

### 智能化特性
- **内容自动识别**: AI分析URL模式，自动区分内容页和服务页
- **栏目智能发现**: 自动识别具有分页特征的栏目页面
- **规则自动生成**: 为每个栏目生成专用的翻页和提取规则

## 注意事项
1. 所有栏目规则必须经过测试验证才能保存
2. 使用数据库事务确保规则生成的原子性
3. 分页处理优先使用统一浏览器处理方案
4. AI服务调用需要配置相应的API密钥
5. 大量抓取时注意请求频率限制，避免被目标网站封禁

## 相关文档
- 详细架构设计: `doc/01 类功能架构.md`
- 核心信息说明: `doc/00 核心信息.md`
- 待办事项: `doc/02 Todo.md`
- 系统重构分析: `doc/系统重构分析报告.md`
- 调整记录: `doc/03 调整记录.md`


### 数据获取原则
- 不应该在任何地方直接抓取HTML，而是应该通过SiteMap的方法来获取。这是数据访问层设计的基本原则