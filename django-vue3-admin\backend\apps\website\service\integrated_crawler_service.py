# -*- coding: utf-8 -*-
"""
集成爬虫服务 - 整合所有步骤的业务逻辑
将01、02、03脚本的核心业务逻辑抽离并封装为服务方法

功能：
1. 步骤1：站点深度初始化 (01_create_site.py)
2. 步骤2-1：生成URL分析报告 (02_1_analysis_site_rules.py)
3. 步骤2-2：应用规则分类 (02_2_applay_rules_to_sitemap.py)
4. 步骤3：AI内容分类 (03_update_content_type_by_ai.py)
"""

import os
import signal
import time
from typing import Dict, Any, Optional
from urllib.parse import urlparse
from django.utils import timezone
from django.db.models import Q
from django.db import transaction

from apps.website.models import Site, SiteMap
from apps.website.service.url_analysis_service_v2 import URLAnalysisServiceV2
from apps.website.service.site_map_service import SiteMapService
from apps.website.service.content_type_matcher import ContentTypeMatcher
from apps.website.service.ai_services.dify_service import DifyService
from apps.website.enums import WebsiteContentType
import re


class InterruptHandler:
    """中断处理器 - 用于优雅处理KeyboardInterrupt"""
    
    def __init__(self):
        self.interrupted = False
        self.original_handler = None
    
    def __enter__(self):
        self.original_handler = signal.signal(signal.SIGINT, self._signal_handler)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self.original_handler:
            signal.signal(signal.SIGINT, self.original_handler)
    
    def _signal_handler(self, signum, frame):
        print("\n⚠️  收到中断信号，正在优雅停止...")
        self.interrupted = True
    
    def check_interrupted(self):
        """检查是否被中断"""
        if self.interrupted:
            raise KeyboardInterrupt("用户中断了执行")


class IntegratedCrawlerService:
    """集成爬虫服务 - 整合所有步骤的业务逻辑"""
    
    def __init__(self):
        self.url_analysis_service = URLAnalysisServiceV2()
        self.site_map_service = SiteMapService()
        self.dify_service = DifyService()
    
    def create_or_get_site(self, site_config: Dict[str, str]) -> Dict[str, Any]:
        """
        创建或获取站点对象
        
        Args:
            site_config: 站点配置字典，包含name, domain, start_url, description
            
        Returns:
            包含站点对象和操作结果的字典
        """
        try:
            # 检查站点是否已存在
            existing_site = Site.objects.filter(
                domain=site_config['domain']
            ).first()
            
            if existing_site:
                return {
                    'success': True,
                    'site': existing_site,
                    'created': False,
                    'message': f"站点已存在: {existing_site.name}"
                }
            
            # 创建新站点
            site = Site.objects.create(
                name=site_config['name'],
                domain=site_config['domain'],
                start_url=site_config['start_url'],
                description=site_config.get('description', ''),
                is_active=True
            )
            
            return {
                'success': True,
                'site': site,
                'created': True,
                'message': f"站点创建成功: {site.name}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"站点创建失败: {str(e)}"
            }
    
    def step01_create_site(self, site_config: Dict[str, str]) -> Dict[str, Any]:
        """
        步骤1：站点深度初始化
        基于01_create_site.py的deep_initialize_site函数
        
        Args:
            site_config: 站点配置字典
            
        Returns:
            初始化结果字典
        """
        # 首先创建或获取站点
        site_result = self.create_or_get_site(site_config)
        if not site_result['success']:
            return site_result
        
        target_site = site_result['site']
        
        try:
            # 解析域名
            parsed_url = urlparse(target_site.start_url)
            domain = parsed_url.netloc
            
            # 移除www前缀获取基础域名
            if domain.startswith('www.'):
                base_domain = domain[4:]
            else:
                base_domain = domain
            
            print(f"🔄 正在创建首页SiteMap记录: {target_site.start_url}")
            
            # 为首页URL创建SiteMap记录
            homepage_sitemap = self.site_map_service.create_or_get_sitemap(
                site=target_site,
                url=target_site.start_url,
                title=f"{target_site.name}首页"
            )
            
            print(f"🔄 正在分析站点结构: {target_site.start_url}")
            
            # 通过SiteMap获取HTML内容并进行分析
            html_content = homepage_sitemap.get_html_content()
            if not html_content:
                return {
                    'success': False,
                    'error': f"无法获取首页HTML内容: {target_site.start_url}",
                    'site': target_site
                }
            
            # 使用新的HTML分析方法
            analysis_result = self.url_analysis_service.extract_and_classify_urls_from_html(
                html_content, target_site.start_url, base_domain
            )
            
            if not analysis_result.get('success'):
                return {
                    'success': False,
                    'error': f"URL分析失败: {analysis_result.get('error')}",
                    'site': target_site
                }
            
            print(f"✅ 站点结构分析完成")
            
            # 获取分类结果
            classifications = analysis_result.get('classifications', {})
            statistics = analysis_result.get('statistics', {})
            
            # 创建当前域名的站点地图
            site_map_result = None
            current_domain_urls = self.url_analysis_service.get_current_domain_urls(classifications)
            if current_domain_urls:
                print(f"🔄 正在创建站点地图: {len(current_domain_urls)} 个URL")
                site_map_result = self.site_map_service.create_site_maps_from_urls(
                    target_site, current_domain_urls
                )
                print(f"✅ 站点地图创建完成")
            
            # 发现和创建子站点
            sub_sites_result = None
            subdomain_urls = self.url_analysis_service.get_subdomain_urls(classifications)
            if subdomain_urls:
                print(f"🔄 正在发现子站点: {len(subdomain_urls)} 个子域名")
                sub_sites_result = self.site_map_service.discover_and_create_sub_sites(
                    target_site, subdomain_urls
                )
                print(f"✅ 子站点发现完成")
            
            # 更新站点描述
            description_parts = [
                f"深度初始化完成 - {timezone.now().strftime('%Y-%m-%d %H:%M')}",
                f"发现链接: {statistics.get('total_urls', 0)} 个",
                f"站点地图: {site_map_result.get('created_count', 0) if site_map_result else 0} 个"
            ]
            
            target_site.description = "; ".join(description_parts)
            target_site.save()
            
            return {
                'success': True,
                'site': target_site,
                'homepage_sitemap': homepage_sitemap,
                'analysis_result': analysis_result,
                'site_map_result': site_map_result,
                'sub_sites_result': sub_sites_result,
                'statistics': statistics
            }
            
        except KeyboardInterrupt:
            return {
                'success': False,
                'error': "站点深度初始化被用户中断",
                'site': target_site,
                'interrupted': True
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"站点深度初始化失败: {str(e)}",
                'site': target_site
            }
    
    def step02_1_analysis_site_rules(self, site: Site) -> Dict[str, Any]:
        """
        步骤2-1：生成URL分析报告
        基于02_1_analysis_site_rules.py的generate_simple_site_markdown函数
        
        Args:
            site: 站点对象
            
        Returns:
            分析结果字典
        """
        try:
            # 获取所有SiteMap记录
            sitemaps = SiteMap.objects.filter(site_id=site.id).order_by('url')
            
            if sitemaps.count() == 0:
                return {
                    'success': False,
                    'error': f"站点 {site.name} 没有SiteMap数据"
                }
            
            # 创建site_info目录
            site_info_dir = "apps/website/debugs/site_info"
            os.makedirs(site_info_dir, exist_ok=True)
            
            # 生成简洁的Markdown内容
            md_content = []
            md_content.append(f"# {site.domain} 域名URL列表")
            md_content.append("")
            md_content.append(f"总计：{sitemaps.count()} 个链接")
            md_content.append("")
            md_content.append("| 链接地址 | 标题 |")
            md_content.append("|----------|------|")
            
            # URL列表
            for sitemap in sitemaps:
                url = sitemap.url
                title = (sitemap.title or "无标题").replace('|', '\\|')  # 转义管道符
                md_content.append(f"| {url} | {title} |")
            
            # 写入文件
            file_path = os.path.join(site_info_dir, f"{site.id}.md")
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(md_content))
            
            return {
                'success': True,
                'site': site,
                'file_path': file_path,
                'sitemap_count': sitemaps.count(),
                'message': f"已生成分析文件: {file_path}"
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': f"生成URL分析报告失败: {str(e)}",
                'site': site
            }
    
    def auto_generate_url_rules(self, site: Site) -> Dict[str, Any]:
        """
        自动生成URL分类规则
        
        Args:
            site: 站点对象
            
        Returns:
            规则生成结果
        """
        try:
            # 获取站点的所有URL
            sitemaps = SiteMap.objects.filter(site_id=site.id).order_by('url')
            
            if sitemaps.count() == 0:
                return {
                    'success': False,
                    'error': f"站点 {site.name} 没有SiteMap数据"
                }
            
            # 构建URL文本（模拟Markdown表格格式）
            urls_text_lines = [
                f"# {site.domain} 域名URL列表",
                "",
                f"总计：{sitemaps.count()} 个链接",
                "",
                "| 链接地址 | 标题 |",
                "|----------|------|"
            ]
            
            for sitemap in sitemaps:
                title = (sitemap.title or "无标题").replace('|', '\\|')  # 转义管道符
                urls_text_lines.append(f"| {sitemap.url} | {title} |")
            
            urls_text = '\n'.join(urls_text_lines)
            
            # 调用Dify服务生成规则
            result = self.dify_service.analyze_url_classification_rules(urls_text)
            
            if result.get('success'):
                rules = result.get('rules', [])
                single_rule = result.get('single_rule', {})
                
                # 验证规则有效性
                validation_result = self._validate_rule(single_rule)
                
                if validation_result['valid']:
                    # 自动保存规则到数据库
                    save_result = self._save_rules_to_database(site, rules)
                    
                    if save_result['success']:
                        return {
                            'success': True,
                            'rules': rules,
                            'single_rule': single_rule,
                            'saved': True,
                            'message': f"成功生成并保存规则: {single_rule.get('pattern')} -> {single_rule.get('content_type')}",
                            'save_details': save_result
                        }
                    else:
                        return {
                            'success': False,
                            'error': f"规则生成成功但保存失败: {save_result['error']}",
                            'rules': rules
                        }
                else:
                    return {
                        'success': False,
                        'error': f"规则验证失败: {validation_result['reason']}",
                        'rules': rules
                    }
            else:
                return {
                    'success': False,
                    'error': f"AI规则生成失败: {result.get('error')}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': f"自动生成规则异常: {str(e)}"
            }
    
    def _validate_rule(self, rule: Dict[str, str]) -> Dict[str, Any]:
        """验证生成的规则是否有效"""
        try:
            pattern = rule.get('pattern')
            content_type = rule.get('content_type')
            
            # 基本格式检查
            if not pattern or not content_type:
                return {'valid': False, 'reason': '规则缺少必要字段'}
            
            # 检查内容类型是否有效
            valid_types = [choice[0] for choice in WebsiteContentType.choices]
            if content_type not in valid_types:
                return {'valid': False, 'reason': f'无效的内容类型: {content_type}'}
            
            # 检查正则表达式是否有效
            try:
                re.compile(pattern)
            except re.error as e:
                return {'valid': False, 'reason': f'无效的正则表达式: {e}'}
            
            # 简单的规则质量检查
            if len(pattern) < 5:
                return {'valid': False, 'reason': '规则过于简单，可能不够准确'}
            
            return {'valid': True, 'reason': '规则验证通过'}
            
        except Exception as e:
            return {'valid': False, 'reason': f'验证过程异常: {e}'}
    
    def _save_rules_to_database(self, site: Site, rules: list) -> Dict[str, Any]:
        """将规则保存到数据库"""
        try:
            # 备份现有规则
            original_rules = site.url_matching_rules.copy() if site.url_matching_rules else []
            
            # 合并新规则（新规则添加到开头，优先级更高）
            updated_rules = rules + original_rules
            
            # 更新站点规则
            site.url_matching_rules = updated_rules
            site.save()
            
            return {
                'success': True,
                'original_count': len(original_rules),
                'new_count': len(rules),
                'total_count': len(updated_rules)
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
    
    def step02_2_apply_rules_to_sitemap(self, site: Site) -> Dict[str, Any]:
        """
        步骤2-2：应用规则分类
        基于02_2_applay_rules_to_sitemap.py的apply_rules_to_sitemap函数
        
        Args:
            site: 站点对象
            
        Returns:
            规则应用结果字典
        """
        print(f"\n📋 步骤2-2：应用规则分类")
        
        max_attempts = 3
        attempt = 0
        result: Dict[str, Any] = {
            'success': False,
            'error': "初始化状态",
            'site': site
        }
        
        while attempt < max_attempts:
            attempt += 1
            print(f"\n🔄 第{attempt}次尝试应用规则...")
            
            # 检查站点是否有规则
            if not site.url_matching_rules:
                print("❌ 站点没有URL匹配规则，尝试自动生成...")
                rule_result = self.auto_generate_url_rules(site)
                if not rule_result.get('success'):
                    print(f"❌ 自动生成规则失败: {rule_result.get('error')}")
                    if attempt == max_attempts:
                        raise Exception(
                            "❌ 无法生成有效的URL匹配规则：\n"
                            f"- 连续{max_attempts}次生成规则都失败\n"
                            f"- 错误信息: {rule_result.get('error')}\n"
                            "- 请检查URL模式或考虑手动创建规则"
                        )
                    continue
                
                # 重新获取站点对象以获取新规则
                site.refresh_from_db()
            
            # 应用规则
            result = self.site_map_service.apply_rules_to_sitemap(site)
            if not result.get('success'):
                print(f"❌ 应用规则失败: {result.get('error')}")
                if attempt == max_attempts:
                    raise Exception(
                        "❌ 无法应用URL匹配规则：\n"
                        f"- 连续{max_attempts}次应用规则都失败\n"
                        f"- 错误信息: {result.get('error')}\n"
                        "- 请检查规则配置或考虑手动修复"
                    )
                continue
            
            # 获取分类统计
            after_stats = result.get('after_stats', {})
            total_records = result.get('total_sitemaps', 0)
            unknown_count = after_stats.get('unknown', 0)
            
            # 检查是否所有记录都是unknown
            if unknown_count == total_records and total_records > 0:
                if attempt == max_attempts:
                    raise Exception(
                        "❌ 无法生成有效的URL匹配规则：\n"
                        f"- 连续{max_attempts}次生成的规则都无法正确分类URL\n"
                        "- 所有URL都被分类为unknown\n"
                        "- 请检查URL模式或考虑手动创建规则"
                    )
                
                print(f"❌ 所有{total_records}个URL都被分类为unknown，尝试重新生成规则...")
                # 重新生成规则
                rule_result = self.auto_generate_url_rules(site)
                if not rule_result.get('success'):
                    print(f"❌ 重新生成规则失败: {rule_result.get('error')}")
                    if attempt == max_attempts:
                        raise Exception(
                            "❌ 无法生成有效的URL匹配规则：\n"
                            f"- 连续{max_attempts}次重新生成规则都失败\n"
                            f"- 错误信息: {rule_result.get('error')}\n"
                            "- 请检查URL模式或考虑手动创建规则"
                        )
                    continue

                print(f"✅ 重新生成规则成功: {rule_result.get('message', '规则已更新')}")
                # 重新获取站点对象以获取新规则
                site.refresh_from_db()
                continue
            
            # 规则应用成功，打印结果
            print("✅ 规则应用成功")
            print(f"   规则数量: {result.get('rules_count', 0)} 条")
            print(f"   更新记录: {result.get('updated_count', 0)} 个")
            print(f"   分类统计: {after_stats}")
            
            # 如果有分类成功的记录，返回结果
            return result
        
        # 这行代码实际上不会执行到，因为最后一次失败会抛出异常
        return result
    
    def step03_update_content_type_by_ai(self, site: Site, limit: int = 50) -> Dict[str, Any]:
        """
        步骤3：AI内容分类
        基于03_update_content_type_by_ai.py的update_sitemap_content_type_by_ai函数
        
        Args:
            site: 站点对象
            limit: 本次处理的最大条目数
            
        Returns:
            AI分类结果字典
        """
        try:
            # 筛选出规则无法识别且AI未判定过的SiteMap记录
            sitemaps_to_process = SiteMap.objects.filter(
                Q(site_id=site.id) &
                ~Q(content_type_rule=WebsiteContentType.CONTENT.value) &
                (Q(content_type_ai__isnull=True) | Q(content_type_ai=''))
            )[:limit]
            
            total_count = sitemaps_to_process.count()
            
            if total_count == 0:
                return {
                    'success': True,
                    'site': site,
                    'message': "没有找到需要AI分析的SiteMap记录",
                    'processed_count': 0,
                    'success_count': 0,
                    'failed_count': 0
                }
            
            # 使用SiteMapService进行AI分析
            
            success_count = 0
            failed_count = 0
            results = []
            interrupted = False
            
            # 使用中断处理器
            with InterruptHandler() as interrupt_handler:
                for i, sitemap in enumerate(sitemaps_to_process, 1):
                    # 检查是否被中断
                    try:
                        interrupt_handler.check_interrupted()
                    except KeyboardInterrupt:
                        print(f"\n⚠️  在处理第 {i}/{total_count} 个URL时被中断")
                        interrupted = True
                        break
                    
                    print(f"🔄 处理 {i}/{total_count}: {sitemap.url}")
                    
                    # 使用SiteMapService进行AI分析和更新
                    result = self.site_map_service.analyze_and_update_content_type(sitemap, site)
                    
                    if result.get('success') and result.get('update_success'):
                        success_count += 1
                        results.append({
                            'url': sitemap.url,
                            'status': 'success',
                            'content_type': result.get('content_type'),
                            'new_links_count': result.get('new_links_count', 0)
                        })
                        print(f"✅ 成功: {result.get('content_type')}")
                    else:
                        failed_count += 1
                        results.append({
                            'url': sitemap.url,
                            'status': 'failed',
                            'error': result.get('error', '未知错误')
                        })
                        print(f"❌ 失败: {result.get('error', '未知错误')}")
                    
                    # 添加短暂延迟，给中断信号处理留出时间
                    time.sleep(0.1)
            
            # 如果被中断，在结果中标记
            result_dict = {
                'success': True,
                'site': site,
                'processed_count': success_count + failed_count,
                'success_count': success_count,
                'failed_count': failed_count,
                'results': results[:10]  # 只返回前10个结果
            }
            
            if interrupted:
                result_dict['interrupted'] = True
                result_dict['message'] = f"处理被用户中断，已完成 {success_count + failed_count}/{total_count} 个URL"
            
            return result_dict
            
        except KeyboardInterrupt:
            return {
                'success': False,
                'error': "AI内容分类被用户中断",
                'site': site,
                'interrupted': True
            }
        except Exception as e:
            return {
                'success': False,
                'error': f"AI内容分类失败: {str(e)}",
                'site': site
            }
    
    def run_full_pipeline(self, site_config: Dict[str, str], ai_limit: int = 50) -> Dict[str, Any]:
        """
        运行完整的前3步流程
        
        Args:
            site_config: 站点配置字典
            ai_limit: AI处理的最大条目数
            
        Returns:
            完整流程执行结果
        """
        pipeline_results = {
            'success': True,
            'site_config': site_config,
            'steps': {}
        }
        
        try:
            # 步骤1：站点深度初始化
            step1_result = self.step01_create_site(site_config)
            pipeline_results['steps']['step01'] = step1_result
            
            if not step1_result['success']:
                pipeline_results['success'] = False
                return pipeline_results
            
            site = step1_result['site']
            
            # 步骤2-1：生成URL分析报告
            step2_1_result = self.step02_1_analysis_site_rules(site)
            pipeline_results['steps']['step02_1'] = step2_1_result
            
            # 步骤2-2：应用规则分类（需要先配置规则）
            step2_2_result = self.step02_2_apply_rules_to_sitemap(site)
            pipeline_results['steps']['step02_2'] = step2_2_result
            
            # 步骤3：AI内容分类
            step3_result = self.step03_update_content_type_by_ai(site, ai_limit)
            pipeline_results['steps']['step03'] = step3_result
            
            # 检查是否有任何步骤失败
            failed_steps = []
            for step_name, step_result in pipeline_results['steps'].items():
                if not step_result.get('success', False):
                    failed_steps.append(step_name)
            
            if failed_steps:
                pipeline_results['success'] = False
                pipeline_results['failed_steps'] = failed_steps
            
            return pipeline_results
            
        except Exception as e:
            pipeline_results['success'] = False
            pipeline_results['error'] = f"流程执行失败: {str(e)}"
            return pipeline_results 