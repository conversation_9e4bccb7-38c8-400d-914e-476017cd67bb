# -*- coding: utf-8 -*-
"""
集成爬虫脚本运行器基础类
用于统一管理所有00开头的集成脚本的共同逻辑

功能：
- 提供统一的脚本执行流程
- 统一的输出格式和错误处理
- 减少代码重复，提高维护性

使用方法：
```python
from integrated_crawler_runner import IntegratedCrawlerRunner

SITE_CONFIG = {
    "name": "站点名称",
    "domain": "域名", 
    "start_url": "起始URL",
    "description": "描述"
}

def main():
    runner = IntegratedCrawlerRunner(SITE_CONFIG)
    runner.run(ai_limit=10)

if __name__ == "__main__":
    main()
```
"""

from typing import Dict, Any
from apps.website.service.site_service import SiteService
from apps.website.service.site_map_service import SiteMapService


class IntegratedCrawlerRunner:
    """集成爬虫脚本运行器基础类"""
    
    def __init__(self, site_config: dict):
        """
        初始化运行器
        
        Args:
            site_config: 站点配置字典，包含name、domain、start_url、description
        """
        self.site_config = site_config
        self.site_service = SiteService()
        self.site_map_service = SiteMapService()
        
        # 验证配置
        required_keys = ['name', 'domain', 'start_url', 'description']
        for key in required_keys:
            if key not in site_config:
                raise ValueError(f"站点配置缺少必需的键: {key}")
    
    def run(self, ai_limit: int = 10):
        """
        执行完整的集成流程
        
        Args:
            ai_limit: AI内容分类的处理数量限制
        """
        try:
            self._print_header()
            
            # 步骤1：站点深度初始化
            print("🚀 执行步骤1：站点深度初始化...")
            step1_result = self.site_service.step01_create_site(self.site_config)
            self.print_step_result("步骤1：站点深度初始化", step1_result)
            
            if not step1_result['success']:
                print("\n❌ 步骤1失败，流程终止")
                return
            
            site = step1_result['site']
            print(f"   站点ID: {site.id}")
            
            # 步骤2-2：应用规则分类
            print("\n🔧 执行步骤2-2：应用规则分类...")
            step2_2_result = self.site_map_service.step02_2_apply_rules_to_sitemap(site)
            self.print_step_result("步骤2-2：应用规则分类", step2_2_result)
            
            # 步骤3：AI内容分类
            print("\n🤖 执行步骤3：AI内容分类...")
            print("💡 提示：如需中断，请按 Ctrl+C")
            step3_result = self.site_map_service.step03_update_content_type_by_ai(site, limit=ai_limit)
            self.print_step_result("步骤3：AI内容分析", step3_result)
            
            # 打印执行总结
            self._print_summary({
                "步骤1": step1_result,
                "步骤2-2": step2_2_result,
                "步骤3": step3_result
            }, site)
            
        except KeyboardInterrupt:
            print("\n\n⚠️  用户中断了执行")
        except Exception as e:
            print(f"\n❌ 执行过程中出现错误: {e}")
            import traceback
            traceback.print_exc()
    
    def run_full_pipeline(self, ai_limit: int = 50) -> Dict[str, Any]:
        """
        运行完整的前3步流程
        
        Args:
            ai_limit: AI处理的最大条目数
            
        Returns:
            完整流程执行结果
        """
        pipeline_results = {
            'success': True,
            'site_config': self.site_config,
            'steps': {}
        }
        
        try:
            # 步骤1：站点深度初始化
            step1_result = self.site_service.step01_create_site(self.site_config)
            pipeline_results['steps']['step01'] = step1_result
            
            if not step1_result['success']:
                pipeline_results['success'] = False
                return pipeline_results
            
            site = step1_result['site']
            
            # 步骤2-2：应用规则分类（需要先配置规则）
            step2_2_result = self.site_map_service.step02_2_apply_rules_to_sitemap(site)
            pipeline_results['steps']['step02_2'] = step2_2_result
            
            # 步骤3：AI内容分类
            step3_result = self.site_map_service.step03_update_content_type_by_ai(site, ai_limit)
            pipeline_results['steps']['step03'] = step3_result
            
            # 检查是否有任何步骤失败
            failed_steps = []
            for step_name, step_result in pipeline_results['steps'].items():
                if not step_result.get('success', False):
                    failed_steps.append(step_name)
            
            if failed_steps:
                pipeline_results['success'] = False
                pipeline_results['failed_steps'] = failed_steps
            
            return pipeline_results
            
        except Exception as e:
            pipeline_results['success'] = False
            pipeline_results['error'] = f"流程执行失败: {str(e)}"
            return pipeline_results
    
    def _print_header(self):
        """打印脚本头部信息"""
        self.print_separator("开始执行完整流程")
        print(f"🏢 站点名称: {self.site_config['name']}")
        print(f"   域名: {self.site_config['domain']}")
        print(f"   起始URL: {self.site_config['start_url']}")
    
    def _print_summary(self, results: dict, site):
        """打印执行总结"""
        self.print_separator("流程执行完成")
        
        success_steps = []
        failed_steps = []
        
        for step_name, result in results.items():
            if result.get('success'):
                success_steps.append(step_name)
            else:
                failed_steps.append(step_name)
        
        print(f"✅ 成功步骤: {', '.join(success_steps)}")
        if failed_steps:
            print(f"❌ 失败步骤: {', '.join(failed_steps)}")
        
        print(f"\n🎉 {self.site_config['name']}站点处理完成！")
        print(f"   站点名称: {site.name}")
        print(f"   站点ID: {site.id}")
        print(f"   域名: {site.domain}")
        
        self.print_separator("后续操作建议")
        print("1. 检查生成的URL分析文件")
        print("2. 根据需要配置URL匹配规则")
        print("3. 可以手动运行单独的调试脚本进行细化调整")
    
    def print_separator(self, title: str, char: str = "=", width: int = 60):
        """打印分隔符"""
        print(f"\n{char*width}")
        print(f"  {title}")
        print(f"{char*width}")
    
    def print_step_result(self, step_name: str, result: dict):
        """打印步骤执行结果"""
        print(f"\n📋 {step_name}")
        
        # 检查是否被中断
        if result.get('interrupted'):
            print("⚠️  执行被中断")
            if result.get('success'):
                print("   部分完成")
            else:
                print("   未完成")
            print(f"   错误: {result.get('error', '用户中断')}")
            return
        
        if result.get('success'):
            print("✅ 执行成功")
            
            # 根据不同步骤显示相关信息
            if 'statistics' in result:
                # 步骤1的统计信息
                stats = result['statistics']
                print(f"   抓取统计: {stats}")
            elif 'file_path' in result:
                # 步骤2-1的文件信息
                print(f"   分析文件: {result['file_path']}")
                print(f"   URL数量: {result.get('sitemap_count', 0)}")
            elif 'rules_count' in result:
                # 步骤2-2的规则信息
                print(f"   规则数量: {result.get('rules_count', 0)} 条")
                print(f"   更新记录: {result.get('updated_count', 0)} 个")
                if 'after_stats' in result:
                    print(f"   分类统计: {result['after_stats']}")
            elif 'processed_count' in result:
                # 步骤3的AI分析信息
                print(f"   处理数量: {result.get('processed_count', 0)}")
                print(f"   成功数量: {result.get('success_count', 0)}")
                if 'error_count' in result and result['error_count'] > 0:
                    print(f"   错误数量: {result['error_count']}")
        else:
            print("❌ 执行失败")
            print(f"   错误: {result.get('error', '未知错误')}")
