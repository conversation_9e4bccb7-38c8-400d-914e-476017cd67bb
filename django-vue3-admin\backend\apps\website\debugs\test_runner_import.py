#!/usr/bin/env python
# -*- coding: utf-8 -*-

from base import init_debug
init_debug()

from integrated_crawler_runner import IntegratedCrawlerRunner
from apps.website.service.site_service import SiteService
from apps.website.service.site_map_service import SiteMapService

def test_runner():
    # 测试配置
    SITE_CONFIG = {
        "name": "测试站点",
        "domain": "test.com", 
        "start_url": "https://test.com",
        "description": "测试描述"
    }
    
    try:
        runner = IntegratedCrawlerRunner(SITE_CONFIG)
        print("✅ IntegratedCrawlerRunner 初始化成功")
        
        # 检查服务是否正确初始化
        print(f"site_service类型: {type(runner.site_service)}")
        print(f"site_map_service类型: {type(runner.site_map_service)}")
        
        # 检查方法是否存在
        print(f"site_service.step01_create_site: {hasattr(runner.site_service, 'step01_create_site')}")
        print(f"site_map_service.step02_2_apply_rules_to_sitemap: {hasattr(runner.site_map_service, 'step02_2_apply_rules_to_sitemap')}")
        print(f"site_map_service.step03_update_content_type_by_ai: {hasattr(runner.site_map_service, 'step03_update_content_type_by_ai')}")
        print(f"runner.run_full_pipeline: {hasattr(runner, 'run_full_pipeline')}")
        
        print("✅ 所有方法检查通过!")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_runner()